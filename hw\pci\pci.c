/*
 * QEMU PCI bus manager
 *
 * Copyright (c) 2004 <PERSON><PERSON><PERSON>
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL
 * THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 * THE SOFTWARE.
 */

#include "qemu/osdep.h"
#include "qemu/datadir.h"
#include "qemu/units.h"
#include "hw/irq.h"
#include "hw/pci/pci.h"
#include "hw/pci/pci_bridge.h"
#include "hw/pci/pci_bus.h"
#include "hw/pci/pci_host.h"
#include "hw/qdev-properties.h"
#include "hw/qdev-properties-system.h"
#include "migration/cpr.h"
#include "migration/qemu-file-types.h"
#include "migration/vmstate.h"
#include "net/net.h"
#include "system/numa.h"
#include "system/runstate.h"
#include "system/system.h"
#include "hw/loader.h"
#include "qemu/error-report.h"
#include "qemu/range.h"
#include "trace.h"
#include "hw/pci/msi.h"
#include "hw/pci/msix.h"
#include "hw/hotplug.h"
#include "hw/boards.h"
#include "hw/nvram/fw_cfg.h"
#include "qapi/error.h"
#include "qemu/cutils.h"
#include "pci-internal.h"

#include "hw/xen/xen.h"
#include "hw/i386/kvm/xen_evtchn.h"

/* 全局变量，标识PCI子系统是否可用 */
bool pci_available = true;

/* 静态函数声明 - 获取设备路径的函数 */
static char *pcibus_get_dev_path(DeviceState *dev);
/* 静态函数声明 - 获取固件设备路径的函数 */
static char *pcibus_get_fw_dev_path(DeviceState *dev);
/* 静态函数声明 - PCI总线复位保持函数 */
static void pcibus_reset_hold(Object *obj, ResetType type);
/* 静态函数声明 - 检查PCIe设备是否有上游端口 */
static bool pcie_has_upstream_port(PCIDevice *dev);

/* PCI总线号属性的getter函数 */
static void prop_pci_busnr_get(Object *obj, Visitor *v, const char *name,
                             void *opaque, Error **errp)
{
    /* 获取PCI设备的总线号 */
    uint8_t busnr = pci_dev_bus_num(PCI_DEVICE(obj));

    /* 通过访问者模式返回总线号 */
    visit_type_uint8(v, name, &busnr, errp);
}

/* PCI总线号属性信息结构体 */
static const PropertyInfo prop_pci_busnr = {
    .type = "busnr",                /* 属性类型名称 */
    .get = prop_pci_busnr_get,      /* 获取属性值的函数 */
};

/* PCI设备的属性定义数组 */
static const Property pci_props[] = {
    /* PCI设备地址属性（设备号和功能号），默认值-1表示自动分配 */
    DEFINE_PROP_PCI_DEVFN("addr", PCIDevice, devfn, -1),
    /* ROM文件路径属性 */
    DEFINE_PROP_STRING("romfile", PCIDevice, romfile),
    /* ROM大小属性，默认最大值表示无限制 */
    DEFINE_PROP_UINT32("romsize", PCIDevice, romsize, UINT32_MAX),
    /* ROM BAR使能属性，-1表示默认行为 */
    DEFINE_PROP_INT32("rombar",  PCIDevice, rom_bar, -1),
    /* 多功能设备能力位，默认关闭 */
    DEFINE_PROP_BIT("multifunction", PCIDevice, cap_present,
                    QEMU_PCI_CAP_MULTIFUNCTION_BITNR, false),
    /* PCIe链路状态数据链路层激活位，默认开启 */
    DEFINE_PROP_BIT("x-pcie-lnksta-dllla", PCIDevice, cap_present,
                    QEMU_PCIE_LNKSTA_DLLLA_BITNR, true),
    /* PCIe扩展能力初始化位，默认开启 */
    DEFINE_PROP_BIT("x-pcie-extcap-init", PCIDevice, cap_present,
                    QEMU_PCIE_EXTCAP_INIT_BITNR, true),
    /* 故障转移配对ID属性 */
    DEFINE_PROP_STRING("failover_pair_id", PCIDevice,
                       failover_pair_id),
    /* ACPI索引属性，默认值0 */
    DEFINE_PROP_UINT32("acpi-index",  PCIDevice, acpi_index, 0),
    /* PCIe不可纠正错误掩码位，默认开启 */
    DEFINE_PROP_BIT("x-pcie-err-unc-mask", PCIDevice, cap_present,
                    QEMU_PCIE_ERR_UNC_MASK_BITNR, true),
    /* PCIe ARI下一功能号为1的位，默认关闭 */
    DEFINE_PROP_BIT("x-pcie-ari-nextfn-1", PCIDevice, cap_present,
                    QEMU_PCIE_ARI_NEXTFN_1_BITNR, false),
    /* 最大反弹缓冲区大小属性，使用默认值 */
    DEFINE_PROP_SIZE32("x-max-bounce-buffer-size", PCIDevice,
                     max_bounce_buffer_size, DEFAULT_MAX_BOUNCE_BUFFER_SIZE),
    /* SR-IOV物理功能属性 */
    DEFINE_PROP_STRING("sriov-pf", PCIDevice, sriov_pf),
    /* PCIe扩展标签位，默认开启 */
    DEFINE_PROP_BIT("x-pcie-ext-tag", PCIDevice, cap_present,
                    QEMU_PCIE_EXT_TAG_BITNR, true),
    /* 总线号属性，使用自定义的属性信息 */
    { .name = "busnr", .info = &prop_pci_busnr },
};

/* PCI总线的虚拟机状态描述结构，用于迁移时保存/恢复总线状态 */
static const VMStateDescription vmstate_pcibus = {
    .name = "PCIBUS",                    /* 状态名称 */
    .version_id = 1,                     /* 当前版本ID */
    .minimum_version_id = 1,             /* 最小兼容版本ID */
    .fields = (const VMStateField[]) {   /* 需要保存的字段数组 */
        /* 保存中断数量，要求源和目标相等 */
        VMSTATE_INT32_EQUAL(nirq, PCIBus, NULL),
        /* 保存中断计数数组，长度由nirq决定 */
        VMSTATE_VARRAY_INT32(irq_count, PCIBus,
                             nirq, 0, vmstate_info_int32,
                             int32_t),
        VMSTATE_END_OF_LIST()            /* 字段列表结束标记 */
    }
};

/* GLib序列比较函数，用于比较两个uint32值 */
static gint g_cmp_uint32(gconstpointer a, gconstpointer b, gpointer user_data)
{
    /* 返回a-b的差值，用于排序 */
    return a - b;
}

/* 获取PCI ACPI索引列表的函数 */
static GSequence *pci_acpi_index_list(void)
{
    /* 静态变量，保存已使用的ACPI索引列表 */
    static GSequence *used_acpi_index_list;

    /* 如果列表未初始化，则创建新的序列 */
    if (!used_acpi_index_list) {
        used_acpi_index_list = g_sequence_new(NULL);
    }
    /* 返回ACPI索引列表 */
    return used_acpi_index_list;
}

/* 设置PCI设备的总线主控状态 */
static void pci_set_master(PCIDevice *d, bool enable)
{
    /* 启用或禁用总线主控内存区域 */
    memory_region_set_enabled(&d->bus_master_enable_region, enable);
    /* 缓存总线主控状态 */
    d->is_master = enable; /* cache the status */
}

/* 初始化PCI设备的总线主控功能 */
static void pci_init_bus_master(PCIDevice *pci_dev)
{
    /* 获取PCI设备的IOMMU地址空间 */
    AddressSpace *dma_as = pci_device_iommu_address_space(pci_dev);

    /* 初始化总线主控使能区域作为DMA地址空间根区域的别名 */
    memory_region_init_alias(&pci_dev->bus_master_enable_region,
                             OBJECT(pci_dev), "bus master",
                             dma_as->root, 0, memory_region_size(dma_as->root));
    /* 默认禁用总线主控 */
    pci_set_master(pci_dev, false);
    /* 将总线主控使能区域添加到容器区域中 */
    memory_region_add_subregion(&pci_dev->bus_master_container_region, 0,
                                &pci_dev->bus_master_enable_region);
}

/* 机器初始化完成时的回调函数，用于初始化PCI总线上所有设备的总线主控功能 */
static void pcibus_machine_done(Notifier *notifier, void *data)
{
    /* 通过container_of宏从notifier获取包含它的PCIBus结构 */
    PCIBus *bus = container_of(notifier, PCIBus, machine_done);
    int i;

    /* 遍历总线上的所有设备槽位 */
    for (i = 0; i < ARRAY_SIZE(bus->devices); ++i) {
        /* 如果设备槽位有设备，则初始化其总线主控功能 */
        if (bus->devices[i]) {
            pci_init_bus_master(bus->devices[i]);
        }
    }
}

/* PCI总线实现函数，在总线创建时调用 */
static void pci_bus_realize(BusState *qbus, Error **errp)
{
    /* 将通用总线状态转换为PCI总线 */
    PCIBus *bus = PCI_BUS(qbus);

    /* 设置机器初始化完成时的通知回调函数 */
    bus->machine_done.notify = pcibus_machine_done;
    /* 注册机器初始化完成通知器 */
    qemu_add_machine_init_done_notifier(&bus->machine_done);

    /* 注册虚拟机状态，用于迁移时保存/恢复总线状态 */
    vmstate_register_any(NULL, &vmstate_pcibus, bus);
}

/* PCIe总线实现函数，在PCIe总线创建时调用 */
static void pcie_bus_realize(BusState *qbus, Error **errp)
{
    /* 将通用总线状态转换为PCI总线 */
    PCIBus *bus = PCI_BUS(qbus);
    Error *local_err = NULL;

    /* 首先调用基础PCI总线的实现函数 */
    pci_bus_realize(qbus, &local_err);
    if (local_err) {
        /* 如果出错，传播错误并返回 */
        error_propagate(errp, local_err);
        return;
    }

    /*
     * PCIe总线可以支持扩展配置空间，条件是：
     * 1. 它是根总线，或者
     * 2. 它上层的总线/桥也支持扩展配置空间
     */
    if (pci_bus_is_root(bus)) {
        /* 如果是根总线，直接启用扩展配置空间 */
        bus->flags |= PCI_BUS_EXTENDED_CONFIG_SPACE;
    } else {
        /* 获取父总线 */
        PCIBus *parent_bus = pci_get_bus(bus->parent_dev);

        /* 如果父总线允许扩展配置空间，则启用 */
        if (pci_bus_allows_extended_config_space(parent_bus)) {
            bus->flags |= PCI_BUS_EXTENDED_CONFIG_SPACE;
        }
    }
}

/* PCI总线销毁函数，在总线被销毁时调用 */
static void pci_bus_unrealize(BusState *qbus)
{
    /* 将通用总线状态转换为PCI总线 */
    PCIBus *bus = PCI_BUS(qbus);

    /* 移除机器初始化完成通知器 */
    qemu_remove_machine_init_done_notifier(&bus->machine_done);

    /* 注销虚拟机状态，清理迁移相关资源 */
    vmstate_unregister(NULL, &vmstate_pcibus, bus);
}

/* 获取PCI总线编号的函数 */
static int pcibus_num(PCIBus *bus)
{
    /* 如果是根总线（PCI主机桥），返回0 */
    if (pci_bus_is_root(bus)) {
        return 0; /* pci host bridge */
    }
    /* 否则从父设备的配置空间中读取次级总线号 */
    return bus->parent_dev->config[PCI_SECONDARY_BUS];
}

/* 获取PCI总线的NUMA节点号，当前实现总是返回未分配状态 */
static uint16_t pcibus_numa_node(PCIBus *bus)
{
    /* 返回NUMA节点未分配标识 */
    return NUMA_NODE_UNASSIGNED;
}

/* 向固件配置添加额外PCI根总线信息的函数 */
bool pci_bus_add_fw_cfg_extra_pci_roots(FWCfgState *fw_cfg,
                                        PCIBus *bus,
                                        Error **errp)
{
    Object *obj;

    /* 如果总线为空，直接返回成功 */
    if (!bus) {
        return true;
    }
    /* 将总线转换为对象 */
    obj = OBJECT(bus);

    /* 向固件配置添加文件，包含额外PCI根总线的信息 */
    return fw_cfg_add_file_from_generator(fw_cfg, obj->parent,
                                          object_get_canonical_path_component(obj),
                                          "etc/extra-pci-roots", errp);
}

/* 为固件配置生成PCI总线数据的函数 */
static GByteArray *pci_bus_fw_cfg_gen_data(Object *obj, Error **errp)
{
    /* 将对象转换为PCI总线 */
    PCIBus *bus = PCI_BUS(obj);
    GByteArray *byte_array;
    /* 额外主机桥数量计数器 */
    uint64_t extra_hosts = 0;

    /* 如果总线为空，返回NULL */
    if (!bus) {
        return NULL;
    }

    /* 遍历所有子总线，查找扩展器根总线 */
    QLIST_FOREACH(bus, &bus->child, sibling) {
        /* look for expander root buses */
        /* 如果是根总线，增加额外主机桥计数 */
        if (pci_bus_is_root(bus)) {
            extra_hosts++;
        }
    }

    /* 如果没有额外的主机桥，返回NULL */
    if (!extra_hosts) {
        return NULL;
    }
    /* 将主机桥数量转换为小端序 */
    extra_hosts = cpu_to_le64(extra_hosts);

    /* 创建新的字节数组 */
    byte_array = g_byte_array_new();
    /* 将额外主机桥数量添加到字节数组中 */
    g_byte_array_append(byte_array,
                        (const void *)&extra_hosts, sizeof(extra_hosts));

    /* 返回包含数据的字节数组 */
    return byte_array;
}

/* PCI总线类初始化函数，设置总线类的各种方法指针 */
static void pci_bus_class_init(ObjectClass *klass, const void *data)
{
    /* 获取各种类的指针 */
    BusClass *k = BUS_CLASS(klass);                           /* 基础总线类 */
    PCIBusClass *pbc = PCI_BUS_CLASS(klass);                  /* PCI总线类 */
    ResettableClass *rc = RESETTABLE_CLASS(klass);            /* 可复位类 */
    FWCfgDataGeneratorClass *fwgc = FW_CFG_DATA_GENERATOR_CLASS(klass); /* 固件配置数据生成器类 */

    /* 设置基础总线类的方法指针 */
    k->print_dev = pcibus_dev_print;           /* 设备打印函数 */
    k->get_dev_path = pcibus_get_dev_path;     /* 获取设备路径函数 */
    k->get_fw_dev_path = pcibus_get_fw_dev_path; /* 获取固件设备路径函数 */
    k->realize = pci_bus_realize;              /* 总线实现函数 */
    k->unrealize = pci_bus_unrealize;          /* 总线销毁函数 */

    /* 设置可复位类的复位阶段处理函数 */
    rc->phases.hold = pcibus_reset_hold;       /* 复位保持阶段处理函数 */

    /* 设置PCI总线类特有的方法指针 */
    pbc->bus_num = pcibus_num;                 /* 获取总线编号函数 */
    pbc->numa_node = pcibus_numa_node;         /* 获取NUMA节点函数 */

    /* 设置固件配置数据生成器的数据获取函数 */
    fwgc->get_data = pci_bus_fw_cfg_gen_data;  /* 固件配置数据生成函数 */
}

/* PCI总线类型信息结构，定义PCI总线的类型特征 */
static const TypeInfo pci_bus_info = {
    .name = TYPE_PCI_BUS,                      /* 类型名称 */
    .parent = TYPE_BUS,                        /* 父类型为通用总线 */
    .instance_size = sizeof(PCIBus),           /* 实例大小 */
    .class_size = sizeof(PCIBusClass),         /* 类大小 */
    .class_init = pci_bus_class_init,          /* 类初始化函数 */
    .interfaces = (const InterfaceInfo[]) {    /* 实现的接口列表 */
        { TYPE_FW_CFG_DATA_GENERATOR_INTERFACE }, /* 固件配置数据生成器接口 */
        { }                                    /* 接口列表结束标记 */
    }
};

/* CXL设备接口类型信息，定义CXL设备的接口特征 */
static const TypeInfo cxl_interface_info = {
    .name          = INTERFACE_CXL_DEVICE,     /* CXL设备接口名称 */
    .parent        = TYPE_INTERFACE,           /* 父类型为通用接口 */
};

/* PCIe设备接口类型信息，定义PCIe设备的接口特征 */
static const TypeInfo pcie_interface_info = {
    .name          = INTERFACE_PCIE_DEVICE,    /* PCIe设备接口名称 */
    .parent        = TYPE_INTERFACE,           /* 父类型为通用接口 */
};

/* 传统PCI设备接口类型信息，定义传统PCI设备的接口特征 */
static const TypeInfo conventional_pci_interface_info = {
    .name          = INTERFACE_CONVENTIONAL_PCI_DEVICE, /* 传统PCI设备接口名称 */
    .parent        = TYPE_INTERFACE,           /* 父类型为通用接口 */
};

/* PCIe总线类初始化函数，设置PCIe总线特有的方法 */
static void pcie_bus_class_init(ObjectClass *klass, const void *data)
{
    /* 获取基础总线类指针 */
    BusClass *k = BUS_CLASS(klass);

    /* 设置PCIe总线的实现函数 */
    k->realize = pcie_bus_realize;
}

/* PCIe总线类型信息，定义PCIe总线的类型特征 */
static const TypeInfo pcie_bus_info = {
    .name = TYPE_PCIE_BUS,                     /* PCIe总线类型名称 */
    .parent = TYPE_PCI_BUS,                    /* 父类型为PCI总线 */
    .class_init = pcie_bus_class_init,         /* 类初始化函数 */
};

/* CXL总线类型信息，定义CXL总线的类型特征 */
static const TypeInfo cxl_bus_info = {
    .name       = TYPE_CXL_BUS,                /* CXL总线类型名称 */
    .parent     = TYPE_PCIE_BUS,               /* 父类型为PCIe总线 */
    .class_init = pcie_bus_class_init,         /* 使用PCIe总线的类初始化函数 */
};

/* 静态函数声明 - 更新PCI设备的内存映射 */
static void pci_update_mappings(PCIDevice *d);
/* 静态函数声明 - PCI中断处理函数 */
static void pci_irq_handler(void *opaque, int irq_num, int level);
/* 静态函数声明 - 添加选项ROM */
static void pci_add_option_rom(PCIDevice *pdev, bool is_default_rom, Error **);
/* 静态函数声明 - 删除选项ROM */
static void pci_del_option_rom(PCIDevice *pdev);

/* 默认的PCI子厂商ID，使用Red Hat QUMRANET的ID */
static uint16_t pci_default_sub_vendor_id = PCI_SUBVENDOR_ID_REDHAT_QUMRANET;
/* 默认的PCI子设备ID，使用QEMU的ID */
static uint16_t pci_default_sub_device_id = PCI_SUBDEVICE_ID_QEMU;

/* 全局PCI主机桥列表 */
PCIHostStateList pci_host_bridges;

/* 获取PCI设备指定寄存器的BAR地址偏移量 */
int pci_bar(PCIDevice *d, int reg)
{
    uint8_t type;

    /* PCIe虚拟功能没有自己的BAR寄存器 */
    assert(!pci_is_vf(d));

    /* 如果不是ROM槽位，返回标准BAR地址 */
    if (reg != PCI_ROM_SLOT)
        return PCI_BASE_ADDRESS_0 + reg * 4;

    /* 获取PCI头类型，屏蔽多功能位 */
    type = d->config[PCI_HEADER_TYPE] & ~PCI_HEADER_TYPE_MULTI_FUNCTION;
    /* 根据头类型返回相应的ROM地址：桥设备使用ROM_ADDRESS1，其他使用ROM_ADDRESS */
    return type == PCI_HEADER_TYPE_BRIDGE ? PCI_ROM_ADDRESS1 : PCI_ROM_ADDRESS;
}

/* 内联函数：获取PCI设备指定中断引脚的状态 */
static inline int pci_irq_state(PCIDevice *d, int irq_num)
{
        /* 从中断状态字段中提取指定中断引脚的状态位 */
        return (d->irq_state >> irq_num) & 0x1;
}

/* 内联函数：设置PCI设备指定中断引脚的状态 */
static inline void pci_set_irq_state(PCIDevice *d, int irq_num, int level)
{
        /* 清除指定中断引脚的状态位 */
        d->irq_state &= ~(0x1 << irq_num);
        /* 设置指定中断引脚的新状态 */
        d->irq_state |= level << irq_num;
}

/* 改变PCI总线上指定中断线的电平状态 */
static void pci_bus_change_irq_level(PCIBus *bus, int irq_num, int change)
{
    /* 断言：中断号必须大于等于0 */
    assert(irq_num >= 0);
    /* 断言：中断号必须小于总线的中断数量 */
    assert(irq_num < bus->nirq);
    /* 更新中断计数器 */
    bus->irq_count[irq_num] += change;
    /* 调用总线的中断设置函数，当计数器非零时激活中断 */
    bus->set_irq(bus->irq_opaque, irq_num, bus->irq_count[irq_num] != 0);
}

/* 改变PCI设备的中断电平，处理中断路由 */
static void pci_change_irq_level(PCIDevice *pci_dev, int irq_num, int change)
{
    PCIBus *bus;
    /* 无限循环，向上遍历PCI层次结构直到找到能处理中断的总线 */
    for (;;) {
        /* 保存设备级中断号用于跟踪 */
        int dev_irq = irq_num;
        /* 获取设备所在的总线 */
        bus = pci_get_bus(pci_dev);
        /* 断言：总线必须有中断映射函数 */
        assert(bus->map_irq);
        /* 将设备中断号映射为总线中断号 */
        irq_num = bus->map_irq(pci_dev, irq_num);
        /* 跟踪中断路由信息 */
        trace_pci_route_irq(dev_irq, DEVICE(pci_dev)->canonical_path, irq_num,
                            pci_bus_is_root(bus) ? "root-complex"
                                    : DEVICE(bus->parent_dev)->canonical_path);
        /* 如果总线有中断设置函数，则停止向上遍历 */
        if (bus->set_irq)
            break;
        /* 否则继续向上到父设备 */
        pci_dev = bus->parent_dev;
    }
    /* 在找到的总线上改变中断电平 */
    pci_bus_change_irq_level(bus, irq_num, change);
}

/* 获取PCI总线上指定中断线的电平状态 */
int pci_bus_get_irq_level(PCIBus *bus, int irq_num)
{
    /* 断言：中断号必须大于等于0 */
    assert(irq_num >= 0);
    /* 断言：中断号必须小于总线的中断数量 */
    assert(irq_num < bus->nirq);
    /* 返回中断计数器的布尔值（非零为真） */
    return !!bus->irq_count[irq_num];
}

/* 在中断状态改变时更新配置空间中的中断状态位 */
static void pci_update_irq_status(PCIDevice *dev)
{
    /* 如果设备有中断状态，设置PCI状态寄存器的中断位 */
    if (dev->irq_state) {
        dev->config[PCI_STATUS] |= PCI_STATUS_INTERRUPT;
    } else {
        /* 否则清除PCI状态寄存器的中断位 */
        dev->config[PCI_STATUS] &= ~PCI_STATUS_INTERRUPT;
    }
}

/* 取消断言PCI设备的所有INTx中断 */
void pci_device_deassert_intx(PCIDevice *dev)
{
    int i;
    /* 遍历所有PCI中断引脚（INTA#-INTD#） */
    for (i = 0; i < PCI_NUM_PINS; ++i) {
        /* 将每个中断引脚设置为低电平（取消断言） */
        pci_irq_handler(dev, i, 0);
    }
}

/* 触发PCI设备的MSI中断 */
static void pci_msi_trigger(PCIDevice *dev, MSIMessage msg)
{
    /* 初始化内存事务属性结构 */
    MemTxAttrs attrs = {};

    /*
     * Xen使用地址的高位来包含PIRQ#的一些位。
     * 因此我们不能只是发送写周期并信任它会被0xfee00000处的APIC捕获，
     * 因为写入的目标可能是例如PIRQ#4166的0x1000fee46000。
     * 所以我们在这里拦截传递，而不是在kvm_send_msi()中。
     */
    if (xen_mode == XEN_EMULATE &&
        xen_evtchn_deliver_pirq_msi(msg.address, msg.data)) {
        /* 如果Xen模式下成功传递了PIRQ MSI，直接返回 */
        return;
    }
    /* 设置请求者ID属性 */
    attrs.requester_id = pci_requester_id(dev);
    /* 通过总线主控地址空间发送MSI消息 */
    address_space_stl_le(&dev->bus_master_as, msg.address, msg.data,
                         attrs, NULL);
}

/*
 * 注册并跟踪PM（电源管理）能力。如果pmcsr寄存器的电源状态字段也启用了wmask，
 * 客户机写入可能会改变设备的PM状态。只有当设备处于D0状态时才启用BAR访问。
 * 返回能力偏移量或负的错误代码。
 */
int pci_pm_init(PCIDevice *d, uint8_t offset, Error **errp)
{
    /* 添加电源管理能力到PCI配置空间 */
    int cap = pci_add_capability(d, PCI_CAP_ID_PM, offset, PCI_PM_SIZEOF, errp);

    /* 如果添加能力失败，返回错误代码 */
    if (cap < 0) {
        return cap;
    }

    /* 保存PM能力的偏移量 */
    d->pm_cap = cap;
    /* 在能力存在标志中设置PM能力位 */
    d->cap_present |= QEMU_PCI_CAP_PM;

    /* 返回能力偏移量 */
    return cap;
}

/* 获取PCI设备的当前电源管理状态 */
static uint8_t pci_pm_state(PCIDevice *d)
{
    uint16_t pmcsr;

    /* 如果设备不支持PM能力，返回0（默认状态） */
    if (!(d->cap_present & QEMU_PCI_CAP_PM)) {
        return 0;
    }

    /* 从配置空间读取PM控制/状态寄存器 */
    pmcsr = pci_get_word(d->config + d->pm_cap + PCI_PM_CTRL);

    /* 返回电源状态位（D0-D3） */
    return pmcsr & PCI_PM_CTRL_STATE_MASK;
}

/*
 * 基于配置空间中存储的新值相对于提供的旧的预写状态来更新PM能力状态。
 * 如果新值被拒绝（不支持或无效转换），则恢复旧值。
 * 返回结果PM状态。
 */
static uint8_t pci_pm_update(PCIDevice *d, uint32_t addr, int l, uint8_t old)
{
    uint16_t pmc;  /* PM能力寄存器 */
    uint8_t new;   /* 新的电源状态 */

    /* 如果设备不支持PM能力或写入地址不覆盖PM控制寄存器，返回旧状态 */
    if (!(d->cap_present & QEMU_PCI_CAP_PM) ||
        !range_covers_byte(addr, l, d->pm_cap + PCI_PM_CTRL)) {
        return old;
    }

    /* 获取新的电源状态 */
    new = pci_pm_state(d);
    /* 如果新状态与旧状态相同，直接返回 */
    if (new == old) {
        return old;
    }

    /* 读取PM能力寄存器，检查支持的电源状态 */
    pmc = pci_get_word(d->config + d->pm_cap + PCI_PM_PMC);

    /*
     * 只有在支持的情况下才允许转换到D1和D2状态。
     * 设备只能转换到更高的D状态或转换到D0状态。
     */
    if ((!(pmc & PCI_PM_CAP_D1) && new == 1) ||      /* D1状态不支持但尝试转换到D1 */
        (!(pmc & PCI_PM_CAP_D2) && new == 2) ||      /* D2状态不支持但尝试转换到D2 */
        (old && new && new < old)) {                 /* 尝试从高D状态转换到低D状态（除了D0） */
        /* 清除电源状态位 */
        pci_word_test_and_clear_mask(d->config + d->pm_cap + PCI_PM_CTRL,
                                     PCI_PM_CTRL_STATE_MASK);
        /* 恢复旧的电源状态 */
        pci_word_test_and_set_mask(d->config + d->pm_cap + PCI_PM_CTRL,
                                   old);
        /* 记录无效的电源状态转换 */
        trace_pci_pm_bad_transition(d->name, pci_dev_bus_num(d),
                                    PCI_SLOT(d->devfn), PCI_FUNC(d->devfn),
                                    old, new);
        return old;
    }

    /* 记录成功的电源状态转换 */
    trace_pci_pm_transition(d->name, pci_dev_bus_num(d), PCI_SLOT(d->devfn),
                            PCI_FUNC(d->devfn), old, new);
    return new;
}

/* 重置PCI设备的所有区域（BAR寄存器） */
static void pci_reset_regions(PCIDevice *dev)
{
    int r;
    /* 如果是虚拟功能设备，直接返回（VF没有自己的BAR） */
    if (pci_is_vf(dev)) {
        return;
    }

    /* 遍历所有PCI区域 */
    for (r = 0; r < PCI_NUM_REGIONS; ++r) {
        /* 获取当前区域的指针 */
        PCIIORegion *region = &dev->io_regions[r];
        /* 如果区域大小为0，跳过 */
        if (!region->size) {
            continue;
        }

        /* 根据区域类型设置BAR寄存器的值 */
        if (!(region->type & PCI_BASE_ADDRESS_SPACE_IO) &&      /* 不是I/O空间 */
            region->type & PCI_BASE_ADDRESS_MEM_TYPE_64) {      /* 是64位内存空间 */
            /* 设置64位BAR寄存器 */
            pci_set_quad(dev->config + pci_bar(dev, r), region->type);
        } else {
            /* 设置32位BAR寄存器 */
            pci_set_long(dev->config + pci_bar(dev, r), region->type);
        }
    }
}

/* 执行PCI设备的实际重置操作 */
static void pci_do_device_reset(PCIDevice *dev)
{
    /* 如果设备支持CPR跳过重置且正在进行CPR恢复，则跳过重置 */
    if ((dev->cap_present & QEMU_PCI_SKIP_RESET_ON_CPR) && cpr_is_incoming()) {
        return;
    }

    /* 取消断言所有INTx中断 */
    pci_device_deassert_intx(dev);
    /* 断言：确保所有中断状态都已清除 */
    assert(dev->irq_state == 0);

    /* 清除所有可写位 */
    /* 清除PCI命令寄存器的可写位和写1清除位 */
    pci_word_test_and_clear_mask(dev->config + PCI_COMMAND,
                                 pci_get_word(dev->wmask + PCI_COMMAND) |
                                 pci_get_word(dev->w1cmask + PCI_COMMAND));
    /* 清除PCI状态寄存器的可写位和写1清除位 */
    pci_word_test_and_clear_mask(dev->config + PCI_STATUS,
                                 pci_get_word(dev->wmask + PCI_STATUS) |
                                 pci_get_word(dev->w1cmask + PCI_STATUS));
    /* 某些设备使PCI_INTERRUPT_LINE的某些位只读 */
    pci_byte_test_and_clear_mask(dev->config + PCI_INTERRUPT_LINE,
                              pci_get_word(dev->wmask + PCI_INTERRUPT_LINE) |
                              pci_get_word(dev->w1cmask + PCI_INTERRUPT_LINE));
    /* 清除缓存行大小寄存器 */
    dev->config[PCI_CACHE_LINE_SIZE] = 0x0;
    /* 默认PM状态是D0 */
    if (dev->cap_present & QEMU_PCI_CAP_PM) {
        /* 清除电源管理控制寄存器的状态位，设置为D0状态 */
        pci_word_test_and_clear_mask(dev->config + dev->pm_cap + PCI_PM_CTRL,
                                     PCI_PM_CTRL_STATE_MASK);
    }
    /* 重置所有PCI区域（BAR寄存器） */
    pci_reset_regions(dev);
    /* 更新内存映射 */
    pci_update_mappings(dev);

    /* 重置MSI中断 */
    msi_reset(dev);
    /* 重置MSI-X中断 */
    msix_reset(dev);
    /* 重置SR-IOV物理功能 */
    pcie_sriov_pf_reset(dev);
}

/*
 * 此函数在#RST和FLR时被调用。
 * 如果设置了PCI_EXP_DEVCTL_BCR_FLR则为FLR（功能级重置）
 */
void pci_device_reset(PCIDevice *dev)
{
    /* 执行设备的冷重置 */
    device_cold_reset(&dev->qdev);
    /* 执行PCI特定的设备重置 */
    pci_do_device_reset(dev);
}

/*
 * 触发给定总线下的PCI总线重置。
 * 在RST#断言时通过bus_cold_reset调用，此时设备已经通过device_cold_reset重置过了。
 */
static void pcibus_reset_hold(Object *obj, ResetType type)
{
    /* 将对象转换为PCI总线 */
    PCIBus *bus = PCI_BUS(obj);
    int i;

    /* 遍历总线上的所有设备槽位 */
    for (i = 0; i < ARRAY_SIZE(bus->devices); ++i) {
        /* 如果槽位有设备，执行PCI设备重置 */
        if (bus->devices[i]) {
            pci_do_device_reset(bus->devices[i]);
        }
    }

    /* 验证所有中断计数器都已清零 */
    for (i = 0; i < bus->nirq; i++) {
        assert(bus->irq_count[i] == 0);
    }
}

/* 注册PCI主机桥到全局列表 */
static void pci_host_bus_register(DeviceState *host)
{
    /* 将设备转换为PCI主机桥 */
    PCIHostState *host_bridge = PCI_HOST_BRIDGE(host);

    /* 将主机桥插入到全局主机桥列表的头部 */
    QLIST_INSERT_HEAD(&pci_host_bridges, host_bridge, next);
}

/* 从全局列表中注销PCI主机桥 */
static void pci_host_bus_unregister(DeviceState *host)
{
    /* 将设备转换为PCI主机桥 */
    PCIHostState *host_bridge = PCI_HOST_BRIDGE(host);

    /* 从全局主机桥列表中移除 */
    QLIST_REMOVE(host_bridge, next);
}

/* 获取PCI设备的根总线 */
PCIBus *pci_device_root_bus(const PCIDevice *d)
{
    /* 获取设备所在的总线 */
    PCIBus *bus = pci_get_bus(d);

    /* 向上遍历总线层次结构直到找到根总线 */
    while (!pci_bus_is_root(bus)) {
        /* 获取当前总线的父设备 */
        d = bus->parent_dev;
        /* 断言：父设备不能为空 */
        assert(d != NULL);

        /* 获取父设备所在的总线 */
        bus = pci_get_bus(d);
    }

    /* 返回根总线 */
    return bus;
}

/* 获取PCI设备根总线的路径字符串 */
const char *pci_root_bus_path(PCIDevice *dev)
{
    /* 获取设备的根总线 */
    PCIBus *rootbus = pci_device_root_bus(dev);
    /* 获取根总线的主机桥 */
    PCIHostState *host_bridge = PCI_HOST_BRIDGE(rootbus->qbus.parent);
    /* 获取主机桥的类 */
    PCIHostBridgeClass *hc = PCI_HOST_BRIDGE_GET_CLASS(host_bridge);

    /* 断言：确保主机桥的总线就是根总线 */
    assert(host_bridge->bus == rootbus);

    /* 如果主机桥类提供了根总线路径函数，使用它 */
    if (hc->root_bus_path) {
        return (*hc->root_bus_path)(host_bridge, rootbus);
    }

    /* 否则返回总线的默认名称 */
    return rootbus->qbus.name;
}

/* 检查PCI总线是否绕过IOMMU */
bool pci_bus_bypass_iommu(PCIBus *bus)
{
    PCIBus *rootbus = bus;
    PCIHostState *host_bridge;

    /* 如果不是根总线，获取根总线 */
    if (!pci_bus_is_root(bus)) {
        rootbus = pci_device_root_bus(bus->parent_dev);
    }

    /* 获取根总线的主机桥 */
    host_bridge = PCI_HOST_BRIDGE(rootbus->qbus.parent);

    /* 断言：确保主机桥的总线就是根总线 */
    assert(host_bridge->bus == rootbus);

    /* 返回主机桥的IOMMU绕过标志 */
    return host_bridge->bypass_iommu;
}

/* PCI根总线的内部初始化函数 */
static void pci_root_bus_internal_init(PCIBus *bus, DeviceState *parent,
                                       MemoryRegion *mem, MemoryRegion *io,
                                       uint8_t devfn_min)
{
    /* 断言：最小设备功能号的功能位必须为0 */
    assert(PCI_FUNC(devfn_min) == 0);
    /* 设置最小设备功能号 */
    bus->devfn_min = devfn_min;
    /* 初始化槽位保留掩码为0（无保留槽位） */
    bus->slot_reserved_mask = 0x0;
    /* 设置内存地址空间 */
    bus->address_space_mem = mem;
    /* 设置I/O地址空间 */
    bus->address_space_io = io;
    /* 设置根总线标志 */
    bus->flags |= PCI_BUS_IS_ROOT;

    /* 初始化子总线列表（主机桥） */
    QLIST_INIT(&bus->child);

    /* 注册主机桥到全局列表 */
    pci_host_bus_register(parent);
}

/* PCI总线的反初始化函数 */
static void pci_bus_uninit(PCIBus *bus)
{
    /* 从全局列表中注销主机桥 */
    pci_host_bus_unregister(BUS(bus)->parent);
}

/* 检查PCI总线是否为PCIe总线 */
bool pci_bus_is_express(const PCIBus *bus)
{
    /* 通过动态类型转换检查是否为PCIe总线类型 */
    return object_dynamic_cast(OBJECT(bus), TYPE_PCIE_BUS);
}

/* 初始化PCI根总线（使用已存在的总线对象） */
void pci_root_bus_init(PCIBus *bus, size_t bus_size, DeviceState *parent,
                       const char *name,
                       MemoryRegion *mem, MemoryRegion *io,
                       uint8_t devfn_min, const char *typename)
{
    /* 初始化基础总线结构 */
    qbus_init(bus, bus_size, typename, parent, name);
    /* 执行PCI根总线的内部初始化 */
    pci_root_bus_internal_init(bus, parent, mem, io, devfn_min);
}

/* 创建新的PCI根总线 */
PCIBus *pci_root_bus_new(DeviceState *parent, const char *name,
                         MemoryRegion *mem, MemoryRegion *io,
                         uint8_t devfn_min, const char *typename)
{
    PCIBus *bus;

    /* 创建新的总线对象 */
    bus = PCI_BUS(qbus_new(typename, parent, name));
    /* 执行PCI根总线的内部初始化 */
    pci_root_bus_internal_init(bus, parent, mem, io, devfn_min);
    return bus;
}

/* 清理PCI根总线 */
void pci_root_bus_cleanup(PCIBus *bus)
{
    /* 执行PCI总线的反初始化 */
    pci_bus_uninit(bus);
    /* 热插拔处理程序的调用者将删除此设备 */
    qbus_unrealize(BUS(bus));
}

/* 设置PCI总线的中断处理 */
void pci_bus_irqs(PCIBus *bus, pci_set_irq_fn set_irq,
                  void *irq_opaque, int nirq)
{
    /* 设置中断设置函数 */
    bus->set_irq = set_irq;
    /* 设置中断不透明数据 */
    bus->irq_opaque = irq_opaque;
    /* 设置中断数量 */
    bus->nirq = nirq;
    /* 释放旧的中断计数数组 */
    g_free(bus->irq_count);
    /* 分配新的中断计数数组并初始化为0 */
    bus->irq_count = g_malloc0(nirq * sizeof(bus->irq_count[0]));
}

/* 设置PCI总线的中断映射函数 */
void pci_bus_map_irqs(PCIBus *bus, pci_map_irq_fn map_irq)
{
    /* 设置中断映射函数 */
    bus->map_irq = map_irq;
}

/* 清理PCI总线的中断设置 */
void pci_bus_irqs_cleanup(PCIBus *bus)
{
    /* 清除中断设置函数 */
    bus->set_irq = NULL;
    /* 清除中断映射函数 */
    bus->map_irq = NULL;
    /* 清除中断不透明数据 */
    bus->irq_opaque = NULL;
    /* 清除中断数量 */
    bus->nirq = 0;
    /* 释放中断计数数组 */
    g_free(bus->irq_count);
    bus->irq_count = NULL;
}

/* 注册PCI根总线（包含中断设置） */
PCIBus *pci_register_root_bus(DeviceState *parent, const char *name,
                              pci_set_irq_fn set_irq, pci_map_irq_fn map_irq,
                              void *irq_opaque,
                              MemoryRegion *mem, MemoryRegion *io,
                              uint8_t devfn_min, int nirq,
                              const char *typename)
{
    PCIBus *bus;

    /* 创建新的PCI根总线 */
    bus = pci_root_bus_new(parent, name, mem, io, devfn_min, typename);
    /* 设置总线的中断处理 */
    pci_bus_irqs(bus, set_irq, irq_opaque, nirq);
    /* 设置总线的中断映射 */
    pci_bus_map_irqs(bus, map_irq);
    return bus;
}

/* 注销PCI根总线 */
void pci_unregister_root_bus(PCIBus *bus)
{
    /* 清理总线的中断设置 */
    pci_bus_irqs_cleanup(bus);
    /* 清理根总线 */
    pci_root_bus_cleanup(bus);
}

/* 获取PCI总线编号 */
int pci_bus_num(PCIBus *s)
{
    /* 调用总线类的bus_num方法 */
    return PCI_BUS_GET_CLASS(s)->bus_num(s);
}

/* 返回PCI总线层次结构的最小和最大总线编号 */
void pci_bus_range(PCIBus *bus, int *min_bus, int *max_bus)
{
    int i;
    /* 初始化最小和最大总线号为当前总线号 */
    *min_bus = *max_bus = pci_bus_num(bus);

    /* 遍历总线上的所有设备 */
    for (i = 0; i < ARRAY_SIZE(bus->devices); ++i) {
        PCIDevice *dev = bus->devices[i];

        /* 如果设备存在且是PCI桥 */
        if (dev && IS_PCI_BRIDGE(dev)) {
            /* 更新最小总线号（考虑次级总线） */
            *min_bus = MIN(*min_bus, dev->config[PCI_SECONDARY_BUS]);
            /* 更新最大总线号（考虑下级总线） */
            *max_bus = MAX(*max_bus, dev->config[PCI_SUBORDINATE_BUS]);
        }
    }
}

/* 获取PCI总线的NUMA节点 */
int pci_bus_numa_node(PCIBus *bus)
{
    /* 调用总线类的numa_node方法 */
    return PCI_BUS_GET_CLASS(bus)->numa_node(bus);
}

/* 从虚拟机状态文件中获取PCI配置设备数据 */
static int get_pci_config_device(QEMUFile *f, void *pv, size_t size,
                                 const VMStateField *field)
{
    /* 通过container_of宏获取包含config的PCIDevice结构 */
    PCIDevice *s = container_of(pv, PCIDevice, config);
    uint8_t *config;
    int i;

    /* 断言：确保大小与PCI配置空间大小匹配 */
    assert(size == pci_config_size(s));
    /* 分配临时配置缓冲区 */
    config = g_malloc(size);

    /* 从文件中读取配置数据 */
    qemu_get_buffer(f, config, size);
    /* 验证配置数据的有效性 */
    for (i = 0; i < size; ++i) {
        /* 检查读取的配置与当前配置的差异是否在允许范围内 */
        if ((config[i] ^ s->config[i]) &
            s->cmask[i] & ~s->wmask[i] & ~s->w1cmask[i]) {
            /* 报告配置数据错误 */
            error_report("%s: Bad config data: i=0x%x read: %x device: %x "
                         "cmask: %x wmask: %x w1cmask:%x", __func__,
                         i, config[i], s->config[i],
                         s->cmask[i], s->wmask[i], s->w1cmask[i]);
            g_free(config);
            return -EINVAL;
        }
    }
    /* 复制有效的配置数据到设备 */
    memcpy(s->config, config, size);

    /* 更新设备的内存映射 */
    pci_update_mappings(s);
    /* 如果是PCI桥，更新桥的映射 */
    if (IS_PCI_BRIDGE(s)) {
        pci_bridge_update_mappings(PCI_BRIDGE(s));
    }

    /* 根据命令寄存器设置总线主控状态 */
    pci_set_master(s, pci_get_word(s->config + PCI_COMMAND)
                      & PCI_COMMAND_MASTER);

    /* 释放临时缓冲区 */
    g_free(config);
    return 0;
}

/* 将PCI配置设备数据写入虚拟机状态文件 */
static int put_pci_config_device(QEMUFile *f, void *pv, size_t size,
                                 const VMStateField *field, JSONWriter *vmdesc)
{
    /* 获取配置数据指针 */
    const uint8_t **v = pv;
    /* 断言：确保大小与PCI配置空间大小匹配 */
    assert(size == pci_config_size(container_of(pv, PCIDevice, config)));
    /* 将配置数据写入文件 */
    qemu_put_buffer(f, *v, size);

    return 0;
}

/* PCI配置的虚拟机状态信息结构 */
static const VMStateInfo vmstate_info_pci_config = {
    .name = "pci config",              /* 状态名称 */
    .get  = get_pci_config_device,     /* 获取函数 */
    .put  = put_pci_config_device,     /* 写入函数 */
};

/* 从虚拟机状态文件中获取PCI中断状态 */
static int get_pci_irq_state(QEMUFile *f, void *pv, size_t size,
                             const VMStateField *field)
{
    /* 通过container_of宏获取包含irq_state的PCIDevice结构 */
    PCIDevice *s = container_of(pv, PCIDevice, irq_state);
    uint32_t irq_state[PCI_NUM_PINS];
    int i;
    /* 读取所有PCI中断引脚的状态 */
    for (i = 0; i < PCI_NUM_PINS; ++i) {
        /* 从文件中读取大端序32位中断状态 */
        irq_state[i] = qemu_get_be32(f);
        /* 验证中断状态值必须为0或1 */
        if (irq_state[i] != 0x1 && irq_state[i] != 0) {
            fprintf(stderr, "irq state %d: must be 0 or 1.\n",
                    irq_state[i]);
            return -EINVAL;
        }
    }

    /* 设置所有中断引脚的状态到设备 */
    for (i = 0; i < PCI_NUM_PINS; ++i) {
        pci_set_irq_state(s, i, irq_state[i]);
    }

    return 0;
}

/* 将PCI中断状态写入虚拟机状态文件 */
static int put_pci_irq_state(QEMUFile *f, void *pv, size_t size,
                             const VMStateField *field, JSONWriter *vmdesc)
{
    int i;
    /* 通过container_of宏获取包含irq_state的PCIDevice结构 */
    PCIDevice *s = container_of(pv, PCIDevice, irq_state);

    /* 写入所有PCI中断引脚的状态 */
    for (i = 0; i < PCI_NUM_PINS; ++i) {
        /* 以大端序32位格式写入中断状态 */
        qemu_put_be32(f, pci_irq_state(s, i));
    }

    return 0;
}

/* PCI中断状态的虚拟机状态信息结构 */
static const VMStateInfo vmstate_info_pci_irq_state = {
    .name = "pci irq state",           /* 状态名称 */
    .get  = get_pci_irq_state,         /* 获取函数 */
    .put  = put_pci_irq_state,         /* 写入函数 */
};

/* 迁移条件检查：是否为PCIe设备 */
static bool migrate_is_pcie(void *opaque, int version_id)
{
    /* 检查设备是否为PCIe设备 */
    return pci_is_express((PCIDevice *)opaque);
}

/* 迁移条件检查：是否不是PCIe设备 */
static bool migrate_is_not_pcie(void *opaque, int version_id)
{
    /* 检查设备是否不是PCIe设备 */
    return !pci_is_express((PCIDevice *)opaque);
}

/* PCI设备加载后的处理函数 */
static int pci_post_load(void *opaque, int version_id)
{
    /* 执行PCIe SR-IOV物理功能的加载后处理 */
    pcie_sriov_pf_post_load(opaque);
    return 0;
}

/* PCI设备的虚拟机状态描述结构 */
const VMStateDescription vmstate_pci_device = {
    .name = "PCIDevice",                    /* 状态名称 */
    .version_id = 2,                        /* 当前版本ID */
    .minimum_version_id = 1,                /* 最小兼容版本ID */
    .post_load = pci_post_load,             /* 加载后处理函数 */
    .fields = (const VMStateField[]) {
        /* 版本ID字段（小端序正整数） */
        VMSTATE_INT32_POSITIVE_LE(version_id, PCIDevice),
        /* 非PCIe设备的配置空间（256字节） */
        VMSTATE_BUFFER_UNSAFE_INFO_TEST(config, PCIDevice,
                                   migrate_is_not_pcie,
                                   0, vmstate_info_pci_config,
                                   PCI_CONFIG_SPACE_SIZE),
        /* PCIe设备的配置空间（4096字节） */
        VMSTATE_BUFFER_UNSAFE_INFO_TEST(config, PCIDevice,
                                   migrate_is_pcie,
                                   0, vmstate_info_pci_config,
                                   PCIE_CONFIG_SPACE_SIZE),
        /* 中断状态字段（版本2开始） */
        VMSTATE_BUFFER_UNSAFE_INFO(irq_state, PCIDevice, 2,
                                   vmstate_info_pci_irq_state,
                                   PCI_NUM_PINS * sizeof(int32_t)),
        VMSTATE_END_OF_LIST()
    }
};


/* 保存PCI设备状态到文件 */
void pci_device_save(PCIDevice *s, QEMUFile *f)
{
    /* 清除中断状态位：它隐含在我们正在保存的irq_state中。
     * 这使我们与从不设置或清除此位的旧设备兼容。 */
    s->config[PCI_STATUS] &= ~PCI_STATUS_INTERRUPT;
    /* 保存虚拟机状态 */
    vmstate_save_state(f, &vmstate_pci_device, s, NULL);
    /* 恢复中断状态位 */
    pci_update_irq_status(s);
}

/* 从文件加载PCI设备状态 */
int pci_device_load(PCIDevice *s, QEMUFile *f)
{
    int ret;
    /* 加载虚拟机状态 */
    ret = vmstate_load_state(f, &vmstate_pci_device, s, s->version_id);
    /* 恢复中断状态位 */
    pci_update_irq_status(s);
    return ret;
}

/* 设置PCI设备的默认子系统ID */
static void pci_set_default_subsystem_id(PCIDevice *pci_dev)
{
    /* 设置子系统厂商ID为默认值 */
    pci_set_word(pci_dev->config + PCI_SUBSYSTEM_VENDOR_ID,
                 pci_default_sub_vendor_id);
    /* 设置子系统设备ID为默认值 */
    pci_set_word(pci_dev->config + PCI_SUBSYSTEM_ID,
                 pci_default_sub_device_id);
}

/*
 * 解析PCI设备地址字符串：
 * 格式：[[<domain>:]<bus>:]<slot>，如果funcp == NULL则出错时返回-1
 *       [[<domain>:]<bus>:]<slot>.<func>，出错时返回-1
 */
static int pci_parse_devaddr(const char *addr, int *domp, int *busp,
                             unsigned int *slotp, unsigned int *funcp)
{
    const char *p;
    char *e;
    unsigned long val;
    unsigned long dom = 0, bus = 0;    /* 域和总线号 */
    unsigned int slot = 0;             /* 槽位号 */
    unsigned int func = 0;             /* 功能号 */

    p = addr;
    /* 解析第一个十六进制数值 */
    val = strtoul(p, &e, 16);
    if (e == p)
        return -1;
    /* 如果后面跟着冒号，说明这是总线号或域号 */
    if (*e == ':') {
        bus = val;
        p = e + 1;
        /* 解析第二个数值 */
        val = strtoul(p, &e, 16);
        if (e == p)
            return -1;
        /* 如果又有冒号，说明第一个是域号，第二个是总线号 */
        if (*e == ':') {
            dom = bus;
            bus = val;
            p = e + 1;
            /* 解析第三个数值（槽位号） */
            val = strtoul(p, &e, 16);
            if (e == p)
                return -1;
        }
    }

    /* 最后解析的数值是槽位号 */
    slot = val;

    /* 如果需要解析功能号 */
    if (funcp != NULL) {
        /* 功能号前必须有点号分隔符 */
        if (*e != '.')
            return -1;

        p = e + 1;
        /* 解析功能号 */
        val = strtoul(p, &e, 16);
        if (e == p)
            return -1;

        func = val;
    }

    /* 如果funcp == NULL，则func为0 */
    /* 验证各字段的取值范围 */
    if (dom > 0xffff || bus > 0xff || slot > 0x1f || func > 7)
        return -1;

    /* 确保字符串已完全解析 */
    if (*e)
        return -1;

    /* 返回解析结果 */
    *domp = dom;
    *busp = bus;
    *slotp = slot;
    if (funcp != NULL)
        *funcp = func;
    return 0;
}

/* 初始化PCI设备的配置掩码（只读位标识） */
static void pci_init_cmask(PCIDevice *dev)
{
    /* 厂商ID和设备ID为只读 */
    pci_set_word(dev->cmask + PCI_VENDOR_ID, 0xffff);
    pci_set_word(dev->cmask + PCI_DEVICE_ID, 0xffff);
    /* 状态寄存器中的能力列表位为只读 */
    dev->cmask[PCI_STATUS] = PCI_STATUS_CAP_LIST;
    /* 修订ID为只读 */
    dev->cmask[PCI_REVISION_ID] = 0xff;
    /* 编程接口为只读 */
    dev->cmask[PCI_CLASS_PROG] = 0xff;
    /* 设备类别为只读 */
    pci_set_word(dev->cmask + PCI_CLASS_DEVICE, 0xffff);
    /* 头部类型为只读 */
    dev->cmask[PCI_HEADER_TYPE] = 0xff;
    /* 能力列表指针为只读 */
    dev->cmask[PCI_CAPABILITY_LIST] = 0xff;
}

/* 初始化PCI设备的写掩码（可写位标识） */
static void pci_init_wmask(PCIDevice *dev)
{
    /* 获取配置空间大小 */
    int config_size = pci_config_size(dev);

    /* 缓存行大小寄存器可写 */
    dev->wmask[PCI_CACHE_LINE_SIZE] = 0xff;
    /* 中断线寄存器可写 */
    dev->wmask[PCI_INTERRUPT_LINE] = 0xff;
    /* 设置命令寄存器的可写位 */
    pci_set_word(dev->wmask + PCI_COMMAND,
                 PCI_COMMAND_IO | PCI_COMMAND_MEMORY | PCI_COMMAND_MASTER |
                 PCI_COMMAND_INTX_DISABLE);
    /* 设置系统错误使能位为可写 */
    pci_word_test_and_set_mask(dev->wmask + PCI_COMMAND, PCI_COMMAND_SERR);

    /* 设置配置头部之后的所有字节为可写 */
    memset(dev->wmask + PCI_CONFIG_HEADER_SIZE, 0xff,
           config_size - PCI_CONFIG_HEADER_SIZE);
}

/* 初始化PCI设备的写1清除掩码 */
static void pci_init_w1cmask(PCIDevice *dev)
{
    /*
     * 注意：即使对于只读位，设置w1cmask也是可以的，
     * 只要它们的值硬连线为0。
     */
    /* 设置状态寄存器中的写1清除位 */
    pci_set_word(dev->w1cmask + PCI_STATUS,
                 PCI_STATUS_PARITY | PCI_STATUS_SIG_TARGET_ABORT |
                 PCI_STATUS_REC_TARGET_ABORT | PCI_STATUS_REC_MASTER_ABORT |
                 PCI_STATUS_SIG_SYSTEM_ERROR | PCI_STATUS_DETECTED_PARITY);
}

/* 初始化PCI桥设备的掩码 */
static void pci_init_mask_bridge(PCIDevice *d)
{
    /* 设置总线号寄存器为可写：
       PCI_PRIMARY_BUS, PCI_SECONDARY_BUS, PCI_SUBORDINATE_BUS 和
       PCI_SEC_LATENCY_TIMER */
    memset(d->wmask + PCI_PRIMARY_BUS, 0xff, 4);

    /* 设置基址和限制寄存器 */
    /* I/O基址和限制寄存器 */
    d->wmask[PCI_IO_BASE] = PCI_IO_RANGE_MASK & 0xff;
    d->wmask[PCI_IO_LIMIT] = PCI_IO_RANGE_MASK & 0xff;
    /* 内存基址和限制寄存器 */
    pci_set_word(d->wmask + PCI_MEMORY_BASE,
                 PCI_MEMORY_RANGE_MASK & 0xffff);
    pci_set_word(d->wmask + PCI_MEMORY_LIMIT,
                 PCI_MEMORY_RANGE_MASK & 0xffff);
    /* 预取内存基址和限制寄存器 */
    pci_set_word(d->wmask + PCI_PREF_MEMORY_BASE,
                 PCI_PREF_RANGE_MASK & 0xffff);
    pci_set_word(d->wmask + PCI_PREF_MEMORY_LIMIT,
                 PCI_PREF_RANGE_MASK & 0xffff);

    /* 预取内存的高32位基址和限制寄存器 */
    memset(d->wmask + PCI_PREF_BASE_UPPER32, 0xff, 8);

    /* 设置支持的内存和I/O类型 */
    /* 设置为16位I/O地址解码 */
    d->config[PCI_IO_BASE] |= PCI_IO_RANGE_TYPE_16;
    d->config[PCI_IO_LIMIT] |= PCI_IO_RANGE_TYPE_16;
    /* 设置为64位预取内存地址解码 */
    pci_word_test_and_set_mask(d->config + PCI_PREF_MEMORY_BASE,
                               PCI_PREF_RANGE_TYPE_64);
    pci_word_test_and_set_mask(d->config + PCI_PREF_MEMORY_LIMIT,
                               PCI_PREF_RANGE_TYPE_64);

    /*
     * TODO: 桥默认使用10位VGA解码，但我们目前只实现16位解码（不支持别名）。
     */
    /* 设置桥控制寄存器的可写位 */
    pci_set_word(d->wmask + PCI_BRIDGE_CONTROL,
                 PCI_BRIDGE_CTL_PARITY |        /* 奇偶校验错误响应使能 */
                 PCI_BRIDGE_CTL_SERR |          /* 系统错误使能 */
                 PCI_BRIDGE_CTL_ISA |           /* ISA使能 */
                 PCI_BRIDGE_CTL_VGA |           /* VGA使能 */
                 PCI_BRIDGE_CTL_VGA_16BIT |     /* VGA 16位解码 */
                 PCI_BRIDGE_CTL_MASTER_ABORT |  /* 主控中止模式 */
                 PCI_BRIDGE_CTL_BUS_RESET |     /* 次级总线重置 */
                 PCI_BRIDGE_CTL_FAST_BACK |     /* 快速后退到后退使能 */
                 PCI_BRIDGE_CTL_DISCARD |       /* 主控丢弃定时器 */
                 PCI_BRIDGE_CTL_SEC_DISCARD |   /* 次级丢弃定时器 */
                 PCI_BRIDGE_CTL_DISCARD_SERR);  /* 丢弃定时器系统错误使能 */
    /* 下面的代码不起作用，因为我们从不设置此位，放在这里是为了完整性。 */
    pci_set_word(d->w1cmask + PCI_BRIDGE_CONTROL,
                 PCI_BRIDGE_CTL_DISCARD_STATUS);
    /* 设置I/O范围类型掩码为只读 */
    d->cmask[PCI_IO_BASE] |= PCI_IO_RANGE_TYPE_MASK;
    d->cmask[PCI_IO_LIMIT] |= PCI_IO_RANGE_TYPE_MASK;
    /* 设置预取内存范围类型掩码为只读 */
    pci_word_test_and_set_mask(d->cmask + PCI_PREF_MEMORY_BASE,
                               PCI_PREF_RANGE_TYPE_MASK);
    pci_word_test_and_set_mask(d->cmask + PCI_PREF_MEMORY_LIMIT,
                               PCI_PREF_RANGE_TYPE_MASK);
}

/* 初始化PCI设备的多功能设置 */
static void pci_init_multifunction(PCIBus *bus, PCIDevice *dev, Error **errp)
{
    /* 获取设备的槽位号 */
    uint8_t slot = PCI_SLOT(dev->devfn);
    uint8_t func;

    /* 如果设备支持多功能，设置多功能位 */
    if (dev->cap_present & QEMU_PCI_CAP_MULTIFUNCTION) {
        dev->config[PCI_HEADER_TYPE] |= PCI_HEADER_TYPE_MULTI_FUNCTION;
    }

    /* SR/IOV在这里不处理 */
    if (pci_is_vf(dev)) {
        return;
    }

    /*
     * 多功能位有两种解释方式：
     *   - 所有功能都必须将该位设置为1。
     *     例如：Intel X53
     *   - 功能0必须设置该位，但其余功能（> 0）
     *     允许将该位保持为0。
     *     例如：PIIX3（也在qemu中）、PIIX4（也在qemu中）、ICH10
     *
     * 所以操作系统（至少Linux）只检查功能0的位，
     * 而不查看功能> 0的位。
     *
     * 下面的检查允许两种解释。
     */
    if (PCI_FUNC(dev->devfn)) {
        /* 如果不是功能0，检查功能0是否设置了多功能位 */
        PCIDevice *f0 = bus->devices[PCI_DEVFN(slot, 0)];
        if (f0 && !(f0->cap_present & QEMU_PCI_CAP_MULTIFUNCTION)) {
            /* 功能0应该设置多功能位 */
            error_setg(errp, "PCI: single function device can't be populated "
                       "in function %x.%x", slot, PCI_FUNC(dev->devfn));
            return;
        }
        return;
    }

    /* 如果是功能0且支持多功能，直接返回 */
    if (dev->cap_present & QEMU_PCI_CAP_MULTIFUNCTION) {
        return;
    }
    /* 功能0表示单功能，所以功能> 0必须为空 */
    for (func = 1; func < PCI_FUNC_MAX; ++func) {
        PCIDevice *device = bus->devices[PCI_DEVFN(slot, func)];
        if (device && !pci_is_vf(device)) {
            error_setg(errp, "PCI: %x.0 indicates single function, "
                       "but %x.%x is already populated.",
                       slot, slot, func);
            return;
        }
    }
}

static void pci_config_alloc(PCIDevice *pci_dev)
{
    int config_size = pci_config_size(pci_dev);

    pci_dev->config = g_malloc0(config_size);
    pci_dev->cmask = g_malloc0(config_size);
    pci_dev->wmask = g_malloc0(config_size);
    pci_dev->w1cmask = g_malloc0(config_size);
    pci_dev->used = g_malloc0(config_size);
}

static void pci_config_free(PCIDevice *pci_dev)
{
    g_free(pci_dev->config);
    g_free(pci_dev->cmask);
    g_free(pci_dev->wmask);
    g_free(pci_dev->w1cmask);
    g_free(pci_dev->used);
}

static void do_pci_unregister_device(PCIDevice *pci_dev)
{
    pci_get_bus(pci_dev)->devices[pci_dev->devfn] = NULL;
    pci_config_free(pci_dev);

    if (xen_mode == XEN_EMULATE) {
        xen_evtchn_remove_pci_device(pci_dev);
    }
    if (memory_region_is_mapped(&pci_dev->bus_master_enable_region)) {
        memory_region_del_subregion(&pci_dev->bus_master_container_region,
                                    &pci_dev->bus_master_enable_region);
    }
    address_space_destroy(&pci_dev->bus_master_as);
}

/* Extract PCIReqIDCache into BDF format */
static uint16_t pci_req_id_cache_extract(PCIReqIDCache *cache)
{
    uint8_t bus_n;
    uint16_t result;

    switch (cache->type) {
    case PCI_REQ_ID_BDF:
        result = pci_get_bdf(cache->dev);
        break;
    case PCI_REQ_ID_SECONDARY_BUS:
        bus_n = pci_dev_bus_num(cache->dev);
        result = PCI_BUILD_BDF(bus_n, 0);
        break;
    default:
        error_report("Invalid PCI requester ID cache type: %d",
                     cache->type);
        exit(1);
        break;
    }

    return result;
}

/* Parse bridges up to the root complex and return requester ID
 * cache for specific device.  For full PCIe topology, the cache
 * result would be exactly the same as getting BDF of the device.
 * However, several tricks are required when system mixed up with
 * legacy PCI devices and PCIe-to-PCI bridges.
 *
 * Here we cache the proxy device (and type) not requester ID since
 * bus number might change from time to time.
 */
static PCIReqIDCache pci_req_id_cache_get(PCIDevice *dev)
{
    PCIDevice *parent;
    PCIReqIDCache cache = {
        .dev = dev,
        .type = PCI_REQ_ID_BDF,
    };

    while (!pci_bus_is_root(pci_get_bus(dev))) {
        /* We are under PCI/PCIe bridges */
        parent = pci_get_bus(dev)->parent_dev;
        if (pci_is_express(parent)) {
            if (pcie_cap_get_type(parent) == PCI_EXP_TYPE_PCI_BRIDGE) {
                /* When we pass through PCIe-to-PCI/PCIX bridges, we
                 * override the requester ID using secondary bus
                 * number of parent bridge with zeroed devfn
                 * (pcie-to-pci bridge spec chap 2.3). */
                cache.type = PCI_REQ_ID_SECONDARY_BUS;
                cache.dev = dev;
            }
        } else {
            /* Legacy PCI, override requester ID with the bridge's
             * BDF upstream.  When the root complex connects to
             * legacy PCI devices (including buses), it can only
             * obtain requester ID info from directly attached
             * devices.  If devices are attached under bridges, only
             * the requester ID of the bridge that is directly
             * attached to the root complex can be recognized. */
            cache.type = PCI_REQ_ID_BDF;
            cache.dev = parent;
        }
        dev = parent;
    }

    return cache;
}

uint16_t pci_requester_id(PCIDevice *dev)
{
    return pci_req_id_cache_extract(&dev->requester_id_cache);
}

static bool pci_bus_devfn_available(PCIBus *bus, int devfn)
{
    return !(bus->devices[devfn]);
}

static bool pci_bus_devfn_reserved(PCIBus *bus, int devfn)
{
    return bus->slot_reserved_mask & (1UL << PCI_SLOT(devfn));
}

uint32_t pci_bus_get_slot_reserved_mask(PCIBus *bus)
{
    return bus->slot_reserved_mask;
}

void pci_bus_set_slot_reserved_mask(PCIBus *bus, uint32_t mask)
{
    bus->slot_reserved_mask |= mask;
}

void pci_bus_clear_slot_reserved_mask(PCIBus *bus, uint32_t mask)
{
    bus->slot_reserved_mask &= ~mask;
}

/* -1 for devfn means auto assign */
static PCIDevice *do_pci_register_device(PCIDevice *pci_dev,
                                         const char *name, int devfn,
                                         Error **errp)
{
    PCIDeviceClass *pc = PCI_DEVICE_GET_CLASS(pci_dev);
    PCIConfigReadFunc *config_read = pc->config_read;
    PCIConfigWriteFunc *config_write = pc->config_write;
    Error *local_err = NULL;
    DeviceState *dev = DEVICE(pci_dev);
    PCIBus *bus = pci_get_bus(pci_dev);
    bool is_bridge = IS_PCI_BRIDGE(pci_dev);

    /* Only pci bridges can be attached to extra PCI root buses */
    if (pci_bus_is_root(bus) && bus->parent_dev && !is_bridge) {
        error_setg(errp,
                   "PCI: Only PCI/PCIe bridges can be plugged into %s",
                    bus->parent_dev->name);
        return NULL;
    }

    if (devfn < 0) {
        for(devfn = bus->devfn_min ; devfn < ARRAY_SIZE(bus->devices);
            devfn += PCI_FUNC_MAX) {
            if (pci_bus_devfn_available(bus, devfn) &&
                   !pci_bus_devfn_reserved(bus, devfn)) {
                goto found;
            }
        }
        error_setg(errp, "PCI: no slot/function available for %s, all in use "
                   "or reserved", name);
        return NULL;
    found: ;
    } else if (pci_bus_devfn_reserved(bus, devfn)) {
        error_setg(errp, "PCI: slot %d function %d not available for %s,"
                   " reserved",
                   PCI_SLOT(devfn), PCI_FUNC(devfn), name);
        return NULL;
    } else if (!pci_bus_devfn_available(bus, devfn)) {
        error_setg(errp, "PCI: slot %d function %d not available for %s,"
                   " in use by %s,id=%s",
                   PCI_SLOT(devfn), PCI_FUNC(devfn), name,
                   bus->devices[devfn]->name, bus->devices[devfn]->qdev.id);
        return NULL;
    }

    /*
     * Populating function 0 triggers a scan from the guest that
     * exposes other non-zero functions. Hence we need to ensure that
     * function 0 wasn't added yet.
     */
    if (dev->hotplugged && !pci_is_vf(pci_dev) &&
        pci_get_function_0(pci_dev)) {
        error_setg(errp, "PCI: slot %d function 0 already occupied by %s,"
                   " new func %s cannot be exposed to guest.",
                   PCI_SLOT(pci_get_function_0(pci_dev)->devfn),
                   pci_get_function_0(pci_dev)->name,
                   name);

       return NULL;
    }

    pci_dev->devfn = devfn;
    pci_dev->requester_id_cache = pci_req_id_cache_get(pci_dev);
    pstrcpy(pci_dev->name, sizeof(pci_dev->name), name);

    memory_region_init(&pci_dev->bus_master_container_region, OBJECT(pci_dev),
                       "bus master container", UINT64_MAX);
    address_space_init(&pci_dev->bus_master_as,
                       &pci_dev->bus_master_container_region, pci_dev->name);
    pci_dev->bus_master_as.max_bounce_buffer_size =
        pci_dev->max_bounce_buffer_size;

    if (phase_check(PHASE_MACHINE_READY)) {
        pci_init_bus_master(pci_dev);
    }
    pci_dev->irq_state = 0;
    pci_config_alloc(pci_dev);

    pci_config_set_vendor_id(pci_dev->config, pc->vendor_id);
    pci_config_set_device_id(pci_dev->config, pc->device_id);
    pci_config_set_revision(pci_dev->config, pc->revision);
    pci_config_set_class(pci_dev->config, pc->class_id);

    if (!is_bridge) {
        if (pc->subsystem_vendor_id || pc->subsystem_id) {
            pci_set_word(pci_dev->config + PCI_SUBSYSTEM_VENDOR_ID,
                         pc->subsystem_vendor_id);
            pci_set_word(pci_dev->config + PCI_SUBSYSTEM_ID,
                         pc->subsystem_id);
        } else {
            pci_set_default_subsystem_id(pci_dev);
        }
    } else {
        /* subsystem_vendor_id/subsystem_id are only for header type 0 */
        assert(!pc->subsystem_vendor_id);
        assert(!pc->subsystem_id);
    }
    pci_init_cmask(pci_dev);
    pci_init_wmask(pci_dev);
    pci_init_w1cmask(pci_dev);
    if (is_bridge) {
        pci_init_mask_bridge(pci_dev);
    }
    pci_init_multifunction(bus, pci_dev, &local_err);
    if (local_err) {
        error_propagate(errp, local_err);
        do_pci_unregister_device(pci_dev);
        return NULL;
    }

    if (!config_read)
        config_read = pci_default_read_config;
    if (!config_write)
        config_write = pci_default_write_config;
    pci_dev->config_read = config_read;
    pci_dev->config_write = config_write;
    bus->devices[devfn] = pci_dev;
    pci_dev->version_id = 2; /* Current pci device vmstate version */
    return pci_dev;
}

static void pci_unregister_io_regions(PCIDevice *pci_dev)
{
    PCIIORegion *r;
    int i;

    for(i = 0; i < PCI_NUM_REGIONS; i++) {
        r = &pci_dev->io_regions[i];
        if (!r->size || r->addr == PCI_BAR_UNMAPPED)
            continue;
        memory_region_del_subregion(r->address_space, r->memory);
    }

    pci_unregister_vga(pci_dev);
}

static void pci_qdev_unrealize(DeviceState *dev)
{
    PCIDevice *pci_dev = PCI_DEVICE(dev);
    PCIDeviceClass *pc = PCI_DEVICE_GET_CLASS(pci_dev);

    pci_unregister_io_regions(pci_dev);
    pci_del_option_rom(pci_dev);
    pcie_sriov_unregister_device(pci_dev);

    if (pc->exit) {
        pc->exit(pci_dev);
    }

    pci_device_deassert_intx(pci_dev);
    do_pci_unregister_device(pci_dev);

    pci_dev->msi_trigger = NULL;

    /*
     * clean up acpi-index so it could reused by another device
     */
    if (pci_dev->acpi_index) {
        GSequence *used_indexes = pci_acpi_index_list();

        g_sequence_remove(g_sequence_lookup(used_indexes,
                          GINT_TO_POINTER(pci_dev->acpi_index),
                          g_cmp_uint32, NULL));
    }
}

/* 注册PCI设备的BAR（基址寄存器） */
void pci_register_bar(PCIDevice *pci_dev, int region_num,
                      uint8_t type, MemoryRegion *memory)
{
    PCIIORegion *r;
    uint32_t addr; /* PCI配置空间中的偏移 */
    uint64_t wmask;
    /* 获取内存区域大小 */
    pcibus_t size = memory_region_size(memory);
    uint8_t hdr_type;

    /* 验证区域编号的有效性 */
    assert(region_num >= 0);
    assert(region_num < PCI_NUM_REGIONS);
    /* 验证大小必须是2的幂 */
    assert(is_power_of_2(size));

    /* PCI桥设备（类型1头部）最多只能有2个BAR */
    hdr_type =
        pci_dev->config[PCI_HEADER_TYPE] & ~PCI_HEADER_TYPE_MULTI_FUNCTION;
    assert(hdr_type != PCI_HEADER_TYPE_BRIDGE || region_num < 2);

    /* 获取指定区域的指针 */
    r = &pci_dev->io_regions[region_num];
    /* 确保区域尚未注册 */
    assert(!r->size);
    /* 设置区域属性 */
    r->size = size;
    r->type = type;
    r->memory = memory;
    /* 根据类型设置地址空间（I/O或内存） */
    r->address_space = type & PCI_BASE_ADDRESS_SPACE_IO
                        ? pci_get_bus(pci_dev)->address_space_io
                        : pci_get_bus(pci_dev)->address_space_mem;

    /* 如果是虚拟功能设备 */
    if (pci_is_vf(pci_dev)) {
        /* 获取物理功能设备 */
        PCIDevice *pf = pci_dev->exp.sriov_vf.pf;
        /* 验证BAR类型与物理功能一致 */
        assert(!pf || type == pf->exp.sriov_pf.vf_bar_type[region_num]);

        /* 计算BAR地址 */
        r->addr = pci_bar_address(pci_dev, region_num, r->type, r->size);
        /* 如果地址有效，添加到地址空间 */
        if (r->addr != PCI_BAR_UNMAPPED) {
            memory_region_add_subregion_overlap(r->address_space,
                                                r->addr, r->memory, 1);
        }
    } else {
        /* 初始化为未映射状态 */
        r->addr = PCI_BAR_UNMAPPED;

        /* 计算写掩码（大小对齐） */
        wmask = ~(size - 1);
        if (region_num == PCI_ROM_SLOT) {
            /* ROM使能位是可写的 */
            wmask |= PCI_ROM_ADDRESS_ENABLE;
        }

        /* 获取BAR寄存器地址 */
        addr = pci_bar(pci_dev, region_num);
        /* 设置BAR类型 */
        pci_set_long(pci_dev->config + addr, type);

        /* 如果是64位内存BAR */
        if (!(r->type & PCI_BASE_ADDRESS_SPACE_IO) &&
            r->type & PCI_BASE_ADDRESS_MEM_TYPE_64) {
            /* 设置64位写掩码和配置掩码 */
            pci_set_quad(pci_dev->wmask + addr, wmask);
            pci_set_quad(pci_dev->cmask + addr, ~0ULL);
        } else {
            pci_set_long(pci_dev->wmask + addr, wmask & 0xffffffff);
            pci_set_long(pci_dev->cmask + addr, 0xffffffff);
        }
    }
}

/* 更新PCI设备的VGA区域使能状态 */
static void pci_update_vga(PCIDevice *pci_dev)
{
    uint16_t cmd;  /* 存储PCI命令寄存器的值 */

    /* 如果设备不支持VGA功能，直接返回 */
    if (!pci_dev->has_vga) {
        return;
    }

    /* 从PCI配置空间读取命令寄存器的值 */
    cmd = pci_get_word(pci_dev->config + PCI_COMMAND);

    /* 根据内存命令位设置VGA内存区域的使能状态 */
    memory_region_set_enabled(pci_dev->vga_regions[QEMU_PCI_VGA_MEM],
                              cmd & PCI_COMMAND_MEMORY);
    /* 根据I/O命令位设置VGA低端I/O区域的使能状态 */
    memory_region_set_enabled(pci_dev->vga_regions[QEMU_PCI_VGA_IO_LO],
                              cmd & PCI_COMMAND_IO);
    /* 根据I/O命令位设置VGA高端I/O区域的使能状态 */
    memory_region_set_enabled(pci_dev->vga_regions[QEMU_PCI_VGA_IO_HI],
                              cmd & PCI_COMMAND_IO);
}

/* 注册PCI设备的VGA区域（内存区域和I/O区域） */
void pci_register_vga(PCIDevice *pci_dev, MemoryRegion *mem,
                      MemoryRegion *io_lo, MemoryRegion *io_hi)
{
    /* 获取PCI设备所在的总线 */
    PCIBus *bus = pci_get_bus(pci_dev);

    /* 断言确保设备尚未注册VGA功能 */
    assert(!pci_dev->has_vga);

    /* 断言验证VGA内存区域大小是否正确 */
    assert(memory_region_size(mem) == QEMU_PCI_VGA_MEM_SIZE);
    /* 将VGA内存区域保存到设备的VGA区域数组中 */
    pci_dev->vga_regions[QEMU_PCI_VGA_MEM] = mem;
    /* 将VGA内存区域添加到总线的内存地址空间，使用重叠优先级1 */
    memory_region_add_subregion_overlap(bus->address_space_mem,
                                        QEMU_PCI_VGA_MEM_BASE, mem, 1);

    /* 断言验证VGA低端I/O区域大小是否正确 */
    assert(memory_region_size(io_lo) == QEMU_PCI_VGA_IO_LO_SIZE);
    /* 将VGA低端I/O区域保存到设备的VGA区域数组中 */
    pci_dev->vga_regions[QEMU_PCI_VGA_IO_LO] = io_lo;
    /* 将VGA低端I/O区域添加到总线的I/O地址空间，使用重叠优先级1 */
    memory_region_add_subregion_overlap(bus->address_space_io,
                                        QEMU_PCI_VGA_IO_LO_BASE, io_lo, 1);

    /* 断言验证VGA高端I/O区域大小是否正确 */
    assert(memory_region_size(io_hi) == QEMU_PCI_VGA_IO_HI_SIZE);
    /* 将VGA高端I/O区域保存到设备的VGA区域数组中 */
    pci_dev->vga_regions[QEMU_PCI_VGA_IO_HI] = io_hi;
    /* 将VGA高端I/O区域添加到总线的I/O地址空间，使用重叠优先级1 */
    memory_region_add_subregion_overlap(bus->address_space_io,
                                        QEMU_PCI_VGA_IO_HI_BASE, io_hi, 1);
    /* 标记设备已具有VGA功能 */
    pci_dev->has_vga = true;

    /* 更新VGA区域的使能状态 */
    pci_update_vga(pci_dev);
}

void pci_unregister_vga(PCIDevice *pci_dev)
{
    PCIBus *bus = pci_get_bus(pci_dev);

    if (!pci_dev->has_vga) {
        return;
    }

    memory_region_del_subregion(bus->address_space_mem,
                                pci_dev->vga_regions[QEMU_PCI_VGA_MEM]);
    memory_region_del_subregion(bus->address_space_io,
                                pci_dev->vga_regions[QEMU_PCI_VGA_IO_LO]);
    memory_region_del_subregion(bus->address_space_io,
                                pci_dev->vga_regions[QEMU_PCI_VGA_IO_HI]);
    pci_dev->has_vga = false;
}

pcibus_t pci_get_bar_addr(PCIDevice *pci_dev, int region_num)
{
    return pci_dev->io_regions[region_num].addr;
}

/* 从PCI配置空间获取BAR地址 */
static pcibus_t pci_config_get_bar_addr(PCIDevice *d, int reg,
                                        uint8_t type, pcibus_t size)
{
    pcibus_t new_addr;  /* 存储新的BAR地址 */
    /* 如果不是虚拟功能设备 */
    if (!pci_is_vf(d)) {
        /* 获取BAR寄存器在配置空间中的偏移 */
        int bar = pci_bar(d, reg);
        /* 如果是64位内存BAR */
        if (type & PCI_BASE_ADDRESS_MEM_TYPE_64) {
            /* 读取64位地址值 */
            new_addr = pci_get_quad(d->config + bar);
        } else {
            /* 读取32位地址值 */
            new_addr = pci_get_long(d->config + bar);
        }
    } else {
        /* 处理SR-IOV虚拟功能设备 */
        /* 获取对应的物理功能设备 */
        PCIDevice *pf = d->exp.sriov_vf.pf;
        /* 获取SR-IOV能力结构的偏移 */
        uint16_t sriov_cap = pf->exp.sriov_cap;
        /* 计算VF BAR寄存器的位置 */
        int bar = sriov_cap + PCI_SRIOV_BAR + reg * 4;
        /* 读取VF偏移值 */
        uint16_t vf_offset =
            pci_get_word(pf->config + sriov_cap + PCI_SRIOV_VF_OFFSET);
        /* 读取VF步长值 */
        uint16_t vf_stride =
            pci_get_word(pf->config + sriov_cap + PCI_SRIOV_VF_STRIDE);
        /* 计算当前VF的编号 */
        uint32_t vf_num = d->devfn - (pf->devfn + vf_offset);

        /* 如果VF编号不为0，计算实际的VF索引 */
        if (vf_num) {
            vf_num /= vf_stride;
        }

        /* 根据BAR类型读取基址 */
        if (type & PCI_BASE_ADDRESS_MEM_TYPE_64) {
            /* 从物理功能设备读取64位基址 */
            new_addr = pci_get_quad(pf->config + bar);
        } else {
            /* 从物理功能设备读取32位基址 */
            new_addr = pci_get_long(pf->config + bar);
        }
        /* 根据VF编号和大小计算VF的实际地址 */
        new_addr += vf_num * size;
    }
    /* ROM槽有特定的使能位，保持其完整性 */
    if (reg != PCI_ROM_SLOT) {
        /* 对地址进行大小对齐（清除低位） */
        new_addr &= ~(size - 1);
    }
    /* 返回计算得到的地址 */
    return new_addr;
}

pcibus_t pci_bar_address(PCIDevice *d,
                         int reg, uint8_t type, pcibus_t size)
{
    pcibus_t new_addr, last_addr;
    uint16_t cmd = pci_get_word(d->config + PCI_COMMAND);
    MachineClass *mc = MACHINE_GET_CLASS(qdev_get_machine());
    bool allow_0_address = mc->pci_allow_0_address;

    if (type & PCI_BASE_ADDRESS_SPACE_IO) {
        if (!(cmd & PCI_COMMAND_IO)) {
            return PCI_BAR_UNMAPPED;
        }
        new_addr = pci_config_get_bar_addr(d, reg, type, size);
        last_addr = new_addr + size - 1;
        /* Check if 32 bit BAR wraps around explicitly.
         * TODO: make priorities correct and remove this work around.
         */
        if (last_addr <= new_addr || last_addr >= UINT32_MAX ||
            (!allow_0_address && new_addr == 0)) {
            return PCI_BAR_UNMAPPED;
        }
        return new_addr;
    }

    if (!(cmd & PCI_COMMAND_MEMORY)) {
        return PCI_BAR_UNMAPPED;
    }
    new_addr = pci_config_get_bar_addr(d, reg, type, size);
    /* the ROM slot has a specific enable bit */
    if (reg == PCI_ROM_SLOT && !(new_addr & PCI_ROM_ADDRESS_ENABLE)) {
        return PCI_BAR_UNMAPPED;
    }
    new_addr &= ~(size - 1);
    last_addr = new_addr + size - 1;
    /* NOTE: we do not support wrapping */
    /* XXX: as we cannot support really dynamic
       mappings, we handle specific values as invalid
       mappings. */
    if (last_addr <= new_addr || last_addr == PCI_BAR_UNMAPPED ||
        (!allow_0_address && new_addr == 0)) {
        return PCI_BAR_UNMAPPED;
    }

    /* Now pcibus_t is 64bit.
     * Check if 32 bit BAR wraps around explicitly.
     * Without this, PC ide doesn't work well.
     * TODO: remove this work around.
     */
    if  (!(type & PCI_BASE_ADDRESS_MEM_TYPE_64) && last_addr >= UINT32_MAX) {
        return PCI_BAR_UNMAPPED;
    }

    /*
     * OS is allowed to set BAR beyond its addressable
     * bits. For example, 32 bit OS can set 64bit bar
     * to >4G. Check it. TODO: we might need to support
     * it in the future for e.g. PAE.
     */
    if (last_addr >= HWADDR_MAX) {
        return PCI_BAR_UNMAPPED;
    }

    return new_addr;
}

static void pci_update_mappings(PCIDevice *d)
{
    PCIIORegion *r;
    int i;
    pcibus_t new_addr;

    for(i = 0; i < PCI_NUM_REGIONS; i++) {
        r = &d->io_regions[i];

        /* this region isn't registered */
        if (!r->size)
            continue;

        new_addr = pci_bar_address(d, i, r->type, r->size);
        if (!d->enabled || pci_pm_state(d)) {
            new_addr = PCI_BAR_UNMAPPED;
        }

        /* This bar isn't changed */
        if (new_addr == r->addr)
            continue;

        /* now do the real mapping */
        if (r->addr != PCI_BAR_UNMAPPED) {
            trace_pci_update_mappings_del(d->name, pci_dev_bus_num(d),
                                          PCI_SLOT(d->devfn),
                                          PCI_FUNC(d->devfn),
                                          i, r->addr, r->size);
            memory_region_del_subregion(r->address_space, r->memory);
        }
        r->addr = new_addr;
        if (r->addr != PCI_BAR_UNMAPPED) {
            trace_pci_update_mappings_add(d->name, pci_dev_bus_num(d),
                                          PCI_SLOT(d->devfn),
                                          PCI_FUNC(d->devfn),
                                          i, r->addr, r->size);
            memory_region_add_subregion_overlap(r->address_space,
                                                r->addr, r->memory, 1);
        }
    }

    pci_update_vga(d);
}

int pci_irq_disabled(PCIDevice *d)
{
    return pci_get_word(d->config + PCI_COMMAND) & PCI_COMMAND_INTX_DISABLE;
}

/* Called after interrupt disabled field update in config space,
 * assert/deassert interrupts if necessary.
 * Gets original interrupt disable bit value (before update). */
static void pci_update_irq_disabled(PCIDevice *d, int was_irq_disabled)
{
    int i, disabled = pci_irq_disabled(d);
    if (disabled == was_irq_disabled)
        return;
    for (i = 0; i < PCI_NUM_PINS; ++i) {
        int state = pci_irq_state(d, i);
        pci_change_irq_level(d, i, disabled ? -state : state);
    }
}

uint32_t pci_default_read_config(PCIDevice *d,
                                 uint32_t address, int len)
{
    uint32_t val = 0;

    assert(address + len <= pci_config_size(d));

    if (pci_is_express_downstream_port(d) &&
        ranges_overlap(address, len, d->exp.exp_cap + PCI_EXP_LNKSTA, 2)) {
        pcie_sync_bridge_lnk(d);
    }
    memcpy(&val, d->config + address, len);
    return le32_to_cpu(val);
}

void pci_default_write_config(PCIDevice *d, uint32_t addr, uint32_t val_in, int l)
{
    uint8_t new_pm_state, old_pm_state = pci_pm_state(d);
    int i, was_irq_disabled = pci_irq_disabled(d);
    uint32_t val = val_in;

    assert(addr + l <= pci_config_size(d));

    for (i = 0; i < l; val >>= 8, ++i) {
        uint8_t wmask = d->wmask[addr + i];
        uint8_t w1cmask = d->w1cmask[addr + i];
        assert(!(wmask & w1cmask));
        d->config[addr + i] = (d->config[addr + i] & ~wmask) | (val & wmask);
        d->config[addr + i] &= ~(val & w1cmask); /* W1C: Write 1 to Clear */
    }

    new_pm_state = pci_pm_update(d, addr, l, old_pm_state);

    if (ranges_overlap(addr, l, PCI_BASE_ADDRESS_0, 24) ||
        ranges_overlap(addr, l, PCI_ROM_ADDRESS, 4) ||
        ranges_overlap(addr, l, PCI_ROM_ADDRESS1, 4) ||
        range_covers_byte(addr, l, PCI_COMMAND) ||
        !!new_pm_state != !!old_pm_state) {
        pci_update_mappings(d);
    }

    if (ranges_overlap(addr, l, PCI_COMMAND, 2)) {
        pci_update_irq_disabled(d, was_irq_disabled);
        pci_set_master(d, (pci_get_word(d->config + PCI_COMMAND) &
                          PCI_COMMAND_MASTER) && d->enabled);
    }

    msi_write_config(d, addr, val_in, l);
    msix_write_config(d, addr, val_in, l);
    pcie_sriov_config_write(d, addr, val_in, l);
}

/***********************************************************/
/* generic PCI irq support */

/* 0 <= irq_num <= 3. level must be 0 or 1 */
static void pci_irq_handler(void *opaque, int irq_num, int level)
{
    PCIDevice *pci_dev = opaque;
    int change;

    assert(0 <= irq_num && irq_num < PCI_NUM_PINS);
    assert(level == 0 || level == 1);
    change = level - pci_irq_state(pci_dev, irq_num);
    if (!change)
        return;

    pci_set_irq_state(pci_dev, irq_num, level);
    pci_update_irq_status(pci_dev);
    if (pci_irq_disabled(pci_dev))
        return;
    pci_change_irq_level(pci_dev, irq_num, change);
}

qemu_irq pci_allocate_irq(PCIDevice *pci_dev)
{
    int intx = pci_intx(pci_dev);
    assert(0 <= intx && intx < PCI_NUM_PINS);

    return qemu_allocate_irq(pci_irq_handler, pci_dev, intx);
}

void pci_set_irq(PCIDevice *pci_dev, int level)
{
    int intx = pci_intx(pci_dev);
    pci_irq_handler(pci_dev, intx, level);
}

/* Special hooks used by device assignment */
void pci_bus_set_route_irq_fn(PCIBus *bus, pci_route_irq_fn route_intx_to_irq)
{
    assert(pci_bus_is_root(bus));
    bus->route_intx_to_irq = route_intx_to_irq;
}

PCIINTxRoute pci_device_route_intx_to_irq(PCIDevice *dev, int pin)
{
    PCIBus *bus;

    do {
        int dev_irq = pin;
        bus = pci_get_bus(dev);
        pin = bus->map_irq(dev, pin);
        trace_pci_route_irq(dev_irq, DEVICE(dev)->canonical_path, pin,
                            pci_bus_is_root(bus) ? "root-complex"
                                    : DEVICE(bus->parent_dev)->canonical_path);
        dev = bus->parent_dev;
    } while (dev);

    if (!bus->route_intx_to_irq) {
        error_report("PCI: Bug - unimplemented PCI INTx routing (%s)",
                     object_get_typename(OBJECT(bus->qbus.parent)));
        return (PCIINTxRoute) { PCI_INTX_DISABLED, -1 };
    }

    return bus->route_intx_to_irq(bus->irq_opaque, pin);
}

bool pci_intx_route_changed(PCIINTxRoute *old, PCIINTxRoute *new)
{
    return old->mode != new->mode || old->irq != new->irq;
}

void pci_bus_fire_intx_routing_notifier(PCIBus *bus)
{
    PCIDevice *dev;
    PCIBus *sec;
    int i;

    for (i = 0; i < ARRAY_SIZE(bus->devices); ++i) {
        dev = bus->devices[i];
        if (dev && dev->intx_routing_notifier) {
            dev->intx_routing_notifier(dev);
        }
    }

    QLIST_FOREACH(sec, &bus->child, sibling) {
        pci_bus_fire_intx_routing_notifier(sec);
    }
}

void pci_device_set_intx_routing_notifier(PCIDevice *dev,
                                          PCIINTxRoutingNotifier notifier)
{
    dev->intx_routing_notifier = notifier;
}

/*
 * PCI-to-PCI bridge specification
 * 9.1: Interrupt routing. Table 9-1
 *
 * the PCI Express Base Specification, Revision 2.1
 * *******: INTx interrupt signaling - Rules
 *          the Implementation Note
 *          Table 2-20
 */
/*
 * 0 <= pin <= 3 0 = INTA, 1 = INTB, 2 = INTC, 3 = INTD
 * 0-origin unlike PCI interrupt pin register.
 */
int pci_swizzle_map_irq_fn(PCIDevice *pci_dev, int pin)
{
    return pci_swizzle(PCI_SLOT(pci_dev->devfn), pin);
}

/***********************************************************/
/* monitor info on PCI */

static const pci_class_desc pci_class_descriptions[] =
{
    { 0x0001, "VGA controller", "display"},
    { 0x0100, "SCSI controller", "scsi"},
    { 0x0101, "IDE controller", "ide"},
    { 0x0102, "Floppy controller", "fdc"},
    { 0x0103, "IPI controller", "ipi"},
    { 0x0104, "RAID controller", "raid"},
    { 0x0106, "SATA controller"},
    { 0x0107, "SAS controller"},
    { 0x0180, "Storage controller"},
    { 0x0200, "Ethernet controller", "ethernet"},
    { 0x0201, "Token Ring controller", "token-ring"},
    { 0x0202, "FDDI controller", "fddi"},
    { 0x0203, "ATM controller", "atm"},
    { 0x0280, "Network controller"},
    { 0x0300, "VGA controller", "display", 0x00ff},
    { 0x0301, "XGA controller"},
    { 0x0302, "3D controller"},
    { 0x0380, "Display controller"},
    { 0x0400, "Video controller", "video"},
    { 0x0401, "Audio controller", "sound"},
    { 0x0402, "Phone"},
    { 0x0403, "Audio controller", "sound"},
    { 0x0480, "Multimedia controller"},
    { 0x0500, "RAM controller", "memory"},
    { 0x0501, "Flash controller", "flash"},
    { 0x0580, "Memory controller"},
    { 0x0600, "Host bridge", "host"},
    { 0x0601, "ISA bridge", "isa"},
    { 0x0602, "EISA bridge", "eisa"},
    { 0x0603, "MC bridge", "mca"},
    { 0x0604, "PCI bridge", "pci-bridge"},
    { 0x0605, "PCMCIA bridge", "pcmcia"},
    { 0x0606, "NUBUS bridge", "nubus"},
    { 0x0607, "CARDBUS bridge", "cardbus"},
    { 0x0608, "RACEWAY bridge"},
    { 0x0680, "Bridge"},
    { 0x0700, "Serial port", "serial"},
    { 0x0701, "Parallel port", "parallel"},
    { 0x0800, "Interrupt controller", "interrupt-controller"},
    { 0x0801, "DMA controller", "dma-controller"},
    { 0x0802, "Timer", "timer"},
    { 0x0803, "RTC", "rtc"},
    { 0x0900, "Keyboard", "keyboard"},
    { 0x0901, "Pen", "pen"},
    { 0x0902, "Mouse", "mouse"},
    { 0x0A00, "Dock station", "dock", 0x00ff},
    { 0x0B00, "i386 cpu", "cpu", 0x00ff},
    { 0x0c00, "Firewire controller", "firewire"},
    { 0x0c01, "Access bus controller", "access-bus"},
    { 0x0c02, "SSA controller", "ssa"},
    { 0x0c03, "USB controller", "usb"},
    { 0x0c04, "Fibre channel controller", "fibre-channel"},
    { 0x0c05, "SMBus"},
    { 0, NULL}
};

void pci_for_each_device_under_bus_reverse(PCIBus *bus,
                                           pci_bus_dev_fn fn,
                                           void *opaque)
{
    PCIDevice *d;
    int devfn;

    for (devfn = 0; devfn < ARRAY_SIZE(bus->devices); devfn++) {
        d = bus->devices[ARRAY_SIZE(bus->devices) - 1 - devfn];
        if (d) {
            fn(bus, d, opaque);
        }
    }
}

void pci_for_each_device_reverse(PCIBus *bus, int bus_num,
                                 pci_bus_dev_fn fn, void *opaque)
{
    bus = pci_find_bus_nr(bus, bus_num);

    if (bus) {
        pci_for_each_device_under_bus_reverse(bus, fn, opaque);
    }
}

void pci_for_each_device_under_bus(PCIBus *bus,
                                   pci_bus_dev_fn fn, void *opaque)
{
    PCIDevice *d;
    int devfn;

    for(devfn = 0; devfn < ARRAY_SIZE(bus->devices); devfn++) {
        d = bus->devices[devfn];
        if (d) {
            fn(bus, d, opaque);
        }
    }
}

void pci_for_each_device(PCIBus *bus, int bus_num,
                         pci_bus_dev_fn fn, void *opaque)
{
    bus = pci_find_bus_nr(bus, bus_num);

    if (bus) {
        pci_for_each_device_under_bus(bus, fn, opaque);
    }
}

const pci_class_desc *get_class_desc(int class)
{
    const pci_class_desc *desc;

    desc = pci_class_descriptions;
    while (desc->desc && class != desc->class) {
        desc++;
    }

    return desc;
}

void pci_init_nic_devices(PCIBus *bus, const char *default_model)
{
    qemu_create_nic_bus_devices(&bus->qbus, TYPE_PCI_DEVICE, default_model,
                                "virtio", "virtio-net-pci");
}

bool pci_init_nic_in_slot(PCIBus *rootbus, const char *model,
                          const char *alias, const char *devaddr)
{
    NICInfo *nd = qemu_find_nic_info(model, true, alias);
    int dom, busnr, devfn;
    PCIDevice *pci_dev;
    unsigned slot;
    PCIBus *bus;

    if (!nd) {
        return false;
    }

    if (!devaddr || pci_parse_devaddr(devaddr, &dom, &busnr, &slot, NULL) < 0) {
        error_report("Invalid PCI device address %s for device %s",
                     devaddr, model);
        exit(1);
    }

    if (dom != 0) {
        error_report("No support for non-zero PCI domains");
        exit(1);
    }

    devfn = PCI_DEVFN(slot, 0);

    bus = pci_find_bus_nr(rootbus, busnr);
    if (!bus) {
        error_report("Invalid PCI device address %s for device %s",
                     devaddr, model);
        exit(1);
    }

    pci_dev = pci_new(devfn, model);
    qdev_set_nic_properties(&pci_dev->qdev, nd);
    pci_realize_and_unref(pci_dev, bus, &error_fatal);
    return true;
}

PCIDevice *pci_vga_init(PCIBus *bus)
{
    vga_interface_created = true;
    switch (vga_interface_type) {
    case VGA_CIRRUS:
        return pci_create_simple(bus, -1, "cirrus-vga");
    case VGA_QXL:
        return pci_create_simple(bus, -1, "qxl-vga");
    case VGA_STD:
        return pci_create_simple(bus, -1, "VGA");
    case VGA_VMWARE:
        return pci_create_simple(bus, -1, "vmware-svga");
    case VGA_VIRTIO:
        return pci_create_simple(bus, -1, "virtio-vga");
    case VGA_NONE:
    default: /* Other non-PCI types. Checking for unsupported types is already
                done in vl.c. */
        return NULL;
    }
}

/* Whether a given bus number is in range of the secondary
 * bus of the given bridge device. */
static bool pci_secondary_bus_in_range(PCIDevice *dev, int bus_num)
{
    return !(pci_get_word(dev->config + PCI_BRIDGE_CONTROL) &
             PCI_BRIDGE_CTL_BUS_RESET) /* Don't walk the bus if it's reset. */ &&
        dev->config[PCI_SECONDARY_BUS] <= bus_num &&
        bus_num <= dev->config[PCI_SUBORDINATE_BUS];
}

/* Whether a given bus number is in a range of a root bus */
static bool pci_root_bus_in_range(PCIBus *bus, int bus_num)
{
    int i;

    for (i = 0; i < ARRAY_SIZE(bus->devices); ++i) {
        PCIDevice *dev = bus->devices[i];

        if (dev && IS_PCI_BRIDGE(dev)) {
            if (pci_secondary_bus_in_range(dev, bus_num)) {
                return true;
            }
        }
    }

    return false;
}

PCIBus *pci_find_bus_nr(PCIBus *bus, int bus_num)
{
    PCIBus *sec;

    if (!bus) {
        return NULL;
    }

    if (pci_bus_num(bus) == bus_num) {
        return bus;
    }

    /* Consider all bus numbers in range for the host pci bridge. */
    if (!pci_bus_is_root(bus) &&
        !pci_secondary_bus_in_range(bus->parent_dev, bus_num)) {
        return NULL;
    }

    /* try child bus */
    for (; bus; bus = sec) {
        QLIST_FOREACH(sec, &bus->child, sibling) {
            if (pci_bus_num(sec) == bus_num) {
                return sec;
            }
            /* PXB buses assumed to be children of bus 0 */
            if (pci_bus_is_root(sec)) {
                if (pci_root_bus_in_range(sec, bus_num)) {
                    break;
                }
            } else {
                if (pci_secondary_bus_in_range(sec->parent_dev, bus_num)) {
                    break;
                }
            }
        }
    }

    return NULL;
}

void pci_for_each_bus_depth_first(PCIBus *bus, pci_bus_ret_fn begin,
                                  pci_bus_fn end, void *parent_state)
{
    PCIBus *sec;
    void *state;

    if (!bus) {
        return;
    }

    if (begin) {
        state = begin(bus, parent_state);
    } else {
        state = parent_state;
    }

    QLIST_FOREACH(sec, &bus->child, sibling) {
        pci_for_each_bus_depth_first(sec, begin, end, state);
    }

    if (end) {
        end(bus, state);
    }
}


PCIDevice *pci_find_device(PCIBus *bus, int bus_num, uint8_t devfn)
{
    bus = pci_find_bus_nr(bus, bus_num);

    if (!bus)
        return NULL;

    return bus->devices[devfn];
}

#define ONBOARD_INDEX_MAX (16 * 1024 - 1)

/* PCI设备的QDev实现函数 */
static void pci_qdev_realize(DeviceState *qdev, Error **errp)
{
    /* 将QDev设备转换为PCI设备 */
    PCIDevice *pci_dev = (PCIDevice *)qdev;
    /* 获取PCI设备类 */
    PCIDeviceClass *pc = PCI_DEVICE_GET_CLASS(pci_dev);
    /* 获取对象类 */
    ObjectClass *klass = OBJECT_CLASS(pc);
    Error *local_err = NULL;
    bool is_default_rom;
    uint16_t class_id;

    /*
     * 由systemd限制（参见：udev-builtin-net_id.c）
     * 因为它是唯一已知的用户，遵守它以避免用户
     * 错误配置QEMU然后想知道为什么acpi-index不工作
     */
    if (pci_dev->acpi_index > ONBOARD_INDEX_MAX) {
        error_setg(errp, "acpi-index should be less or equal to %u",
                   ONBOARD_INDEX_MAX);
        return;
    }

    /*
     * 确保acpi-index在所有现有PCI设备中是唯一的
     */
    if (pci_dev->acpi_index) {
        /* 获取已使用的ACPI索引列表 */
        GSequence *used_indexes = pci_acpi_index_list();

        /* 检查索引是否已存在 */
        if (g_sequence_lookup(used_indexes,
                              GINT_TO_POINTER(pci_dev->acpi_index),
                              g_cmp_uint32, NULL)) {
            error_setg(errp, "a PCI device with acpi-index = %" PRIu32
                       " already exist", pci_dev->acpi_index);
            return;
        }
        /* 将新索引插入到已使用列表中 */
        g_sequence_insert_sorted(used_indexes,
                                 GINT_TO_POINTER(pci_dev->acpi_index),
                                 g_cmp_uint32, NULL);
    }

    /* 验证ROM大小必须是2的幂 */
    if (pci_dev->romsize != UINT32_MAX && !is_power_of_2(pci_dev->romsize)) {
        error_setg(errp, "ROM size %u is not a power of two", pci_dev->romsize);
        return;
    }

    /* 为pci_is_express()和pci_config_size()初始化cap_present，
     * 注意混合PCI不会自动设置，需要手动管理QEMU_PCI_CAP_EXPRESS */
    if (object_class_dynamic_cast(klass, INTERFACE_PCIE_DEVICE) &&
       !object_class_dynamic_cast(klass, INTERFACE_CONVENTIONAL_PCI_DEVICE)) {
        /* 设置PCIe能力标志 */
        pci_dev->cap_present |= QEMU_PCI_CAP_EXPRESS;
    }

    if (object_class_dynamic_cast(klass, INTERFACE_CXL_DEVICE)) {
        pci_dev->cap_present |= QEMU_PCIE_CAP_CXL;
    }

    pci_dev = do_pci_register_device(pci_dev,
                                     object_get_typename(OBJECT(qdev)),
                                     pci_dev->devfn, errp);
    if (pci_dev == NULL)
        return;

    if (pc->realize) {
        pc->realize(pci_dev, &local_err);
        if (local_err) {
            error_propagate(errp, local_err);
            do_pci_unregister_device(pci_dev);
            return;
        }
    }

    if (!pcie_sriov_register_device(pci_dev, errp)) {
        pci_qdev_unrealize(DEVICE(pci_dev));
        return;
    }

    /*
     * A PCIe Downstream Port that do not have ARI Forwarding enabled must
     * associate only Device 0 with the device attached to the bus
     * representing the Link from the Port (PCIe base spec rev 4.0 ver 0.3,
     * sec 7.3.1).
     * With ARI, PCI_SLOT() can return non-zero value as the traditional
     * 5-bit Device Number and 3-bit Function Number fields in its associated
     * Routing IDs, Requester IDs and Completer IDs are interpreted as a
     * single 8-bit Function Number. Hence, ignore ARI capable devices.
     */
    if (pci_is_express(pci_dev) &&
        !pcie_find_capability(pci_dev, PCI_EXT_CAP_ID_ARI) &&
        pcie_has_upstream_port(pci_dev) &&
        PCI_SLOT(pci_dev->devfn)) {
        warn_report("PCI: slot %d is not valid for %s,"
                    " parent device only allows plugging into slot 0.",
                    PCI_SLOT(pci_dev->devfn), pci_dev->name);
    }

    if (pci_dev->failover_pair_id) {
        if (!pci_bus_is_express(pci_get_bus(pci_dev))) {
            error_setg(errp, "failover primary device must be on "
                             "PCIExpress bus");
            pci_qdev_unrealize(DEVICE(pci_dev));
            return;
        }
        class_id = pci_get_word(pci_dev->config + PCI_CLASS_DEVICE);
        if (class_id != PCI_CLASS_NETWORK_ETHERNET) {
            error_setg(errp, "failover primary device is not an "
                             "Ethernet device");
            pci_qdev_unrealize(DEVICE(pci_dev));
            return;
        }
        if ((pci_dev->cap_present & QEMU_PCI_CAP_MULTIFUNCTION)
            || (PCI_FUNC(pci_dev->devfn) != 0)) {
            error_setg(errp, "failover: primary device must be in its own "
                              "PCI slot");
            pci_qdev_unrealize(DEVICE(pci_dev));
            return;
        }
        qdev->allow_unplug_during_migration = true;
    }

    /* rom loading */
    is_default_rom = false;
    if (pci_dev->romfile == NULL && pc->romfile != NULL) {
        pci_dev->romfile = g_strdup(pc->romfile);
        is_default_rom = true;
    }

    pci_add_option_rom(pci_dev, is_default_rom, &local_err);
    if (local_err) {
        error_propagate(errp, local_err);
        pci_qdev_unrealize(DEVICE(pci_dev));
        return;
    }

    pci_set_power(pci_dev, true);

    pci_dev->msi_trigger = pci_msi_trigger;
}

/* 内部PCI设备创建函数 */
static PCIDevice *pci_new_internal(int devfn, bool multifunction,
                                   const char *name)
{
    DeviceState *dev;

    /* 创建新的QDev设备 */
    dev = qdev_new(name);
    /* 设置设备地址（设备号和功能号） */
    qdev_prop_set_int32(dev, "addr", devfn);
    /* 设置多功能标志 */
    qdev_prop_set_bit(dev, "multifunction", multifunction);
    /* 返回PCI设备 */
    return PCI_DEVICE(dev);
}

/* 创建多功能PCI设备 */
PCIDevice *pci_new_multifunction(int devfn, const char *name)
{
    /* 调用内部函数，设置多功能为true */
    return pci_new_internal(devfn, true, name);
}

/* 创建单功能PCI设备 */
PCIDevice *pci_new(int devfn, const char *name)
{
    /* 调用内部函数，设置多功能为false */
    return pci_new_internal(devfn, false, name);
}

/* 实现PCI设备并释放引用 */
bool pci_realize_and_unref(PCIDevice *dev, PCIBus *bus, Error **errp)
{
    /* 调用QDev的实现和释放引用函数 */
    return qdev_realize_and_unref(&dev->qdev, &bus->qbus, errp);
}

/* 创建并实现简单的多功能PCI设备 */
PCIDevice *pci_create_simple_multifunction(PCIBus *bus, int devfn,
                                           const char *name)
{
    /* 创建多功能PCI设备 */
    PCIDevice *dev = pci_new_multifunction(devfn, name);
    /* 实现设备并释放引用，出错时致命退出 */
    pci_realize_and_unref(dev, bus, &error_fatal);
    return dev;
}

/* 创建并实现简单的单功能PCI设备 */
PCIDevice *pci_create_simple(PCIBus *bus, int devfn, const char *name)
{
    /* 创建单功能PCI设备 */
    PCIDevice *dev = pci_new(devfn, name);
    /* 实现设备并释放引用，出错时致命退出 */
    pci_realize_and_unref(dev, bus, &error_fatal);
    return dev;
}

static uint8_t pci_find_space(PCIDevice *pdev, uint8_t size)
{
    int offset = PCI_CONFIG_HEADER_SIZE;
    int i;
    for (i = PCI_CONFIG_HEADER_SIZE; i < PCI_CONFIG_SPACE_SIZE; ++i) {
        if (pdev->used[i])
            offset = i + 1;
        else if (i - offset + 1 == size)
            return offset;
    }
    return 0;
}

static uint8_t pci_find_capability_list(PCIDevice *pdev, uint8_t cap_id,
                                        uint8_t *prev_p)
{
    uint8_t next, prev;

    if (!(pdev->config[PCI_STATUS] & PCI_STATUS_CAP_LIST))
        return 0;

    for (prev = PCI_CAPABILITY_LIST; (next = pdev->config[prev]);
         prev = next + PCI_CAP_LIST_NEXT)
        if (pdev->config[next + PCI_CAP_LIST_ID] == cap_id)
            break;

    if (prev_p)
        *prev_p = prev;
    return next;
}

static uint8_t pci_find_capability_at_offset(PCIDevice *pdev, uint8_t offset)
{
    uint8_t next, prev, found = 0;

    if (!(pdev->used[offset])) {
        return 0;
    }

    assert(pdev->config[PCI_STATUS] & PCI_STATUS_CAP_LIST);

    for (prev = PCI_CAPABILITY_LIST; (next = pdev->config[prev]);
         prev = next + PCI_CAP_LIST_NEXT) {
        if (next <= offset && next > found) {
            found = next;
        }
    }
    return found;
}

/* Patch the PCI vendor and device ids in a PCI rom image if necessary.
   This is needed for an option rom which is used for more than one device. */
static void pci_patch_ids(PCIDevice *pdev, uint8_t *ptr, uint32_t size)
{
    uint16_t vendor_id;
    uint16_t device_id;
    uint16_t rom_vendor_id;
    uint16_t rom_device_id;
    uint16_t rom_magic;
    uint16_t pcir_offset;
    uint8_t checksum;

    /* Words in rom data are little endian (like in PCI configuration),
       so they can be read / written with pci_get_word / pci_set_word. */

    /* Only a valid rom will be patched. */
    rom_magic = pci_get_word(ptr);
    if (rom_magic != 0xaa55) {
        trace_pci_bad_rom_magic(rom_magic, 0xaa55);
        return;
    }
    pcir_offset = pci_get_word(ptr + 0x18);
    if (pcir_offset + 8 >= size || memcmp(ptr + pcir_offset, "PCIR", 4)) {
        trace_pci_bad_pcir_offset(pcir_offset);
        return;
    }

    vendor_id = pci_get_word(pdev->config + PCI_VENDOR_ID);
    device_id = pci_get_word(pdev->config + PCI_DEVICE_ID);
    rom_vendor_id = pci_get_word(ptr + pcir_offset + 4);
    rom_device_id = pci_get_word(ptr + pcir_offset + 6);

    trace_pci_rom_and_pci_ids(pdev->romfile, vendor_id, device_id,
                              rom_vendor_id, rom_device_id);

    checksum = ptr[6];

    if (vendor_id != rom_vendor_id) {
        /* Patch vendor id and checksum (at offset 6 for etherboot roms). */
        checksum += (uint8_t)rom_vendor_id + (uint8_t)(rom_vendor_id >> 8);
        checksum -= (uint8_t)vendor_id + (uint8_t)(vendor_id >> 8);
        trace_pci_rom_checksum_change(ptr[6], checksum);
        ptr[6] = checksum;
        pci_set_word(ptr + pcir_offset + 4, vendor_id);
    }

    if (device_id != rom_device_id) {
        /* Patch device id and checksum (at offset 6 for etherboot roms). */
        checksum += (uint8_t)rom_device_id + (uint8_t)(rom_device_id >> 8);
        checksum -= (uint8_t)device_id + (uint8_t)(device_id >> 8);
        trace_pci_rom_checksum_change(ptr[6], checksum);
        ptr[6] = checksum;
        pci_set_word(ptr + pcir_offset + 6, device_id);
    }
}

/* Add an option rom for the device */
static void pci_add_option_rom(PCIDevice *pdev, bool is_default_rom,
                               Error **errp)
{
    int64_t size = 0;
    g_autofree char *path = NULL;
    char name[32];
    const VMStateDescription *vmsd;

    /*
     * In case of incoming migration ROM will come with migration stream, no
     * reason to load the file.  Neither we want to fail if local ROM file
     * mismatches with specified romsize.
     */
    bool load_file = !runstate_check(RUN_STATE_INMIGRATE);

    if (!pdev->romfile || !strlen(pdev->romfile)) {
        return;
    }

    if (!pdev->rom_bar) {
        /*
         * Load rom via fw_cfg instead of creating a rom bar,
         * for 0.11 compatibility.
         */
        int class = pci_get_word(pdev->config + PCI_CLASS_DEVICE);

        /*
         * Hot-plugged devices can't use the option ROM
         * if the rom bar is disabled.
         */
        if (DEVICE(pdev)->hotplugged) {
            error_setg(errp, "Hot-plugged device without ROM bar"
                       " can't have an option ROM");
            return;
        }

        if (class == 0x0300) {
            rom_add_vga(pdev->romfile);
        } else {
            rom_add_option(pdev->romfile, -1);
        }
        return;
    }

    if (pci_is_vf(pdev)) {
        if (pdev->rom_bar > 0) {
            error_setg(errp, "ROM BAR cannot be enabled for SR-IOV VF");
        }

        return;
    }

    if (load_file || pdev->romsize == UINT32_MAX) {
        path = qemu_find_file(QEMU_FILE_TYPE_BIOS, pdev->romfile);
        if (path == NULL) {
            path = g_strdup(pdev->romfile);
        }

        size = get_image_size(path);
        if (size < 0) {
            error_setg(errp, "failed to find romfile \"%s\"", pdev->romfile);
            return;
        } else if (size == 0) {
            error_setg(errp, "romfile \"%s\" is empty", pdev->romfile);
            return;
        } else if (size > 2 * GiB) {
            error_setg(errp,
                       "romfile \"%s\" too large (size cannot exceed 2 GiB)",
                       pdev->romfile);
            return;
        }
        if (pdev->romsize != UINT_MAX) {
            if (size > pdev->romsize) {
                error_setg(errp, "romfile \"%s\" (%u bytes) "
                           "is too large for ROM size %u",
                           pdev->romfile, (uint32_t)size, pdev->romsize);
                return;
            }
        } else {
            pdev->romsize = pow2ceil(size);
        }
    }

    vmsd = qdev_get_vmsd(DEVICE(pdev));
    snprintf(name, sizeof(name), "%s.rom",
             vmsd ? vmsd->name : object_get_typename(OBJECT(pdev)));

    pdev->has_rom = true;
    memory_region_init_rom(&pdev->rom, OBJECT(pdev), name, pdev->romsize,
                           &error_fatal);

    if (load_file) {
        void *ptr = memory_region_get_ram_ptr(&pdev->rom);

        if (load_image_size(path, ptr, size) < 0) {
            error_setg(errp, "failed to load romfile \"%s\"", pdev->romfile);
            return;
        }

        if (is_default_rom) {
            /* Only the default rom images will be patched (if needed). */
            pci_patch_ids(pdev, ptr, size);
        }
    }

    pci_register_bar(pdev, PCI_ROM_SLOT, 0, &pdev->rom);
}

static void pci_del_option_rom(PCIDevice *pdev)
{
    if (!pdev->has_rom)
        return;

    vmstate_unregister_ram(&pdev->rom, &pdev->qdev);
    pdev->has_rom = false;
}

/*
 * On success, pci_add_capability() returns a positive value
 * that the offset of the pci capability.
 * On failure, it sets an error and returns a negative error
 * code.
 */
int pci_add_capability(PCIDevice *pdev, uint8_t cap_id,
                       uint8_t offset, uint8_t size,
                       Error **errp)
{
    uint8_t *config;
    int i, overlapping_cap;

    if (!offset) {
        offset = pci_find_space(pdev, size);
        /* out of PCI config space is programming error */
        assert(offset);
    } else {
        /* Verify that capabilities don't overlap.  Note: device assignment
         * depends on this check to verify that the device is not broken.
         * Should never trigger for emulated devices, but it's helpful
         * for debugging these. */
        for (i = offset; i < offset + size; i++) {
            overlapping_cap = pci_find_capability_at_offset(pdev, i);
            if (overlapping_cap) {
                error_setg(errp, "%s:%02x:%02x.%x "
                           "Attempt to add PCI capability %x at offset "
                           "%x overlaps existing capability %x at offset %x",
                           pci_root_bus_path(pdev), pci_dev_bus_num(pdev),
                           PCI_SLOT(pdev->devfn), PCI_FUNC(pdev->devfn),
                           cap_id, offset, overlapping_cap, i);
                return -EINVAL;
            }
        }
    }

    config = pdev->config + offset;
    config[PCI_CAP_LIST_ID] = cap_id;
    config[PCI_CAP_LIST_NEXT] = pdev->config[PCI_CAPABILITY_LIST];
    pdev->config[PCI_CAPABILITY_LIST] = offset;
    pdev->config[PCI_STATUS] |= PCI_STATUS_CAP_LIST;
    memset(pdev->used + offset, 0xFF, QEMU_ALIGN_UP(size, 4));
    /* Make capability read-only by default */
    memset(pdev->wmask + offset, 0, size);
    /* Check capability by default */
    memset(pdev->cmask + offset, 0xFF, size);
    return offset;
}

/* Unlink capability from the pci config space. */
void pci_del_capability(PCIDevice *pdev, uint8_t cap_id, uint8_t size)
{
    uint8_t prev, offset = pci_find_capability_list(pdev, cap_id, &prev);
    if (!offset)
        return;
    pdev->config[prev] = pdev->config[offset + PCI_CAP_LIST_NEXT];
    /* Make capability writable again */
    memset(pdev->wmask + offset, 0xff, size);
    memset(pdev->w1cmask + offset, 0, size);
    /* Clear cmask as device-specific registers can't be checked */
    memset(pdev->cmask + offset, 0, size);
    memset(pdev->used + offset, 0, QEMU_ALIGN_UP(size, 4));

    if (!pdev->config[PCI_CAPABILITY_LIST])
        pdev->config[PCI_STATUS] &= ~PCI_STATUS_CAP_LIST;
}

uint8_t pci_find_capability(PCIDevice *pdev, uint8_t cap_id)
{
    return pci_find_capability_list(pdev, cap_id, NULL);
}

static char *pci_dev_fw_name(DeviceState *dev, char *buf, int len)
{
    PCIDevice *d = (PCIDevice *)dev;
    const char *name = NULL;
    const pci_class_desc *desc =  pci_class_descriptions;
    int class = pci_get_word(d->config + PCI_CLASS_DEVICE);

    while (desc->desc &&
          (class & ~desc->fw_ign_bits) !=
          (desc->class & ~desc->fw_ign_bits)) {
        desc++;
    }

    if (desc->desc) {
        name = desc->fw_name;
    }

    if (name) {
        pstrcpy(buf, len, name);
    } else {
        snprintf(buf, len, "pci%04x,%04x",
                 pci_get_word(d->config + PCI_VENDOR_ID),
                 pci_get_word(d->config + PCI_DEVICE_ID));
    }

    return buf;
}

static char *pcibus_get_fw_dev_path(DeviceState *dev)
{
    PCIDevice *d = (PCIDevice *)dev;
    char name[33];
    int has_func = !!PCI_FUNC(d->devfn);

    return g_strdup_printf("%s@%x%s%.*x",
                           pci_dev_fw_name(dev, name, sizeof(name)),
                           PCI_SLOT(d->devfn),
                           has_func ? "," : "",
                           has_func,
                           PCI_FUNC(d->devfn));
}

static char *pcibus_get_dev_path(DeviceState *dev)
{
    PCIDevice *d = container_of(dev, PCIDevice, qdev);
    PCIDevice *t;
    int slot_depth;
    /* Path format: Domain:00:Slot.Function:Slot.Function....:Slot.Function.
     * 00 is added here to make this format compatible with
     * domain:Bus:Slot.Func for systems without nested PCI bridges.
     * Slot.Function list specifies the slot and function numbers for all
     * devices on the path from root to the specific device. */
    const char *root_bus_path;
    int root_bus_len;
    char slot[] = ":SS.F";
    int slot_len = sizeof slot - 1 /* For '\0' */;
    int path_len;
    char *path, *p;
    int s;

    root_bus_path = pci_root_bus_path(d);
    root_bus_len = strlen(root_bus_path);

    /* Calculate # of slots on path between device and root. */;
    slot_depth = 0;
    for (t = d; t; t = pci_get_bus(t)->parent_dev) {
        ++slot_depth;
    }

    path_len = root_bus_len + slot_len * slot_depth;

    /* Allocate memory, fill in the terminating null byte. */
    path = g_malloc(path_len + 1 /* For '\0' */);
    path[path_len] = '\0';

    memcpy(path, root_bus_path, root_bus_len);

    /* Fill in slot numbers. We walk up from device to root, so need to print
     * them in the reverse order, last to first. */
    p = path + path_len;
    for (t = d; t; t = pci_get_bus(t)->parent_dev) {
        p -= slot_len;
        s = snprintf(slot, sizeof slot, ":%02x.%x",
                     PCI_SLOT(t->devfn), PCI_FUNC(t->devfn));
        assert(s == slot_len);
        memcpy(p, slot, slot_len);
    }

    return path;
}

static int pci_qdev_find_recursive(PCIBus *bus,
                                   const char *id, PCIDevice **pdev)
{
    DeviceState *qdev = qdev_find_recursive(&bus->qbus, id);
    if (!qdev) {
        return -ENODEV;
    }

    /* roughly check if given qdev is pci device */
    if (object_dynamic_cast(OBJECT(qdev), TYPE_PCI_DEVICE)) {
        *pdev = PCI_DEVICE(qdev);
        return 0;
    }
    return -EINVAL;
}

int pci_qdev_find_device(const char *id, PCIDevice **pdev)
{
    PCIHostState *host_bridge;
    int rc = -ENODEV;

    QLIST_FOREACH(host_bridge, &pci_host_bridges, next) {
        int tmp = pci_qdev_find_recursive(host_bridge->bus, id, pdev);
        if (!tmp) {
            rc = 0;
            break;
        }
        if (tmp != -ENODEV) {
            rc = tmp;
        }
    }

    return rc;
}

MemoryRegion *pci_address_space(PCIDevice *dev)
{
    return pci_get_bus(dev)->address_space_mem;
}

MemoryRegion *pci_address_space_io(PCIDevice *dev)
{
    return pci_get_bus(dev)->address_space_io;
}

static void pci_device_class_init(ObjectClass *klass, const void *data)
{
    DeviceClass *k = DEVICE_CLASS(klass);

    k->realize = pci_qdev_realize;
    k->unrealize = pci_qdev_unrealize;
    k->bus_type = TYPE_PCI_BUS;
    device_class_set_props(k, pci_props);
    object_class_property_set_description(
        klass, "x-max-bounce-buffer-size",
        "Maximum buffer size allocated for bounce buffers used for mapped "
        "access to indirect DMA memory");
}

static void pci_device_class_base_init(ObjectClass *klass, const void *data)
{
    if (!object_class_is_abstract(klass)) {
        ObjectClass *conventional =
            object_class_dynamic_cast(klass, INTERFACE_CONVENTIONAL_PCI_DEVICE);
        ObjectClass *pcie =
            object_class_dynamic_cast(klass, INTERFACE_PCIE_DEVICE);
        ObjectClass *cxl =
            object_class_dynamic_cast(klass, INTERFACE_CXL_DEVICE);
        assert(conventional || pcie || cxl);
    }
}

/*
 * Get IOMMU root bus, aliased bus and devfn of a PCI device
 *
 * IOMMU root bus is needed by all call sites to call into iommu_ops.
 * For call sites which don't need aliased BDF, passing NULL to
 * aliased_[bus|devfn] is allowed.
 *
 * @piommu_bus: return root #PCIBus backed by an IOMMU for the PCI device.
 *
 * @aliased_bus: return aliased #PCIBus of the PCI device, optional.
 *
 * @aliased_devfn: return aliased devfn of the PCI device, optional.
 */
static void pci_device_get_iommu_bus_devfn(PCIDevice *dev,
                                           PCIBus **piommu_bus,
                                           PCIBus **aliased_bus,
                                           int *aliased_devfn)
{
    PCIBus *bus = pci_get_bus(dev);
    PCIBus *iommu_bus = bus;
    int devfn = dev->devfn;

    while (iommu_bus && !iommu_bus->iommu_ops && iommu_bus->parent_dev) {
        PCIBus *parent_bus = pci_get_bus(iommu_bus->parent_dev);

        /*
         * The requester ID of the provided device may be aliased, as seen from
         * the IOMMU, due to topology limitations.  The IOMMU relies on a
         * requester ID to provide a unique AddressSpace for devices, but
         * conventional PCI buses pre-date such concepts.  Instead, the PCIe-
         * to-PCI bridge creates and accepts transactions on behalf of down-
         * stream devices.  When doing so, all downstream devices are masked
         * (aliased) behind a single requester ID.  The requester ID used
         * depends on the format of the bridge devices.  Proper PCIe-to-PCI
         * bridges, with a PCIe capability indicating such, follow the
         * guidelines of chapter 2.3 of the PCIe-to-PCI/X bridge specification,
         * where the bridge uses the seconary bus as the bridge portion of the
         * requester ID and devfn of 00.0.  For other bridges, typically those
         * found on the root complex such as the dmi-to-pci-bridge, we follow
         * the convention of typical bare-metal hardware, which uses the
         * requester ID of the bridge itself.  There are device specific
         * exceptions to these rules, but these are the defaults that the
         * Linux kernel uses when determining DMA aliases itself and believed
         * to be true for the bare metal equivalents of the devices emulated
         * in QEMU.
         */
        if (!pci_bus_is_express(iommu_bus)) {
            PCIDevice *parent = iommu_bus->parent_dev;

            if (pci_is_express(parent) &&
                pcie_cap_get_type(parent) == PCI_EXP_TYPE_PCI_BRIDGE) {
                devfn = PCI_DEVFN(0, 0);
                bus = iommu_bus;
            } else {
                devfn = parent->devfn;
                bus = parent_bus;
            }
        }

        iommu_bus = parent_bus;
    }

    assert(0 <= devfn && devfn < PCI_DEVFN_MAX);
    assert(iommu_bus);

    if (pci_bus_bypass_iommu(bus) || !iommu_bus->iommu_ops) {
        iommu_bus = NULL;
    }

    *piommu_bus = iommu_bus;

    if (aliased_bus) {
        *aliased_bus = bus;
    }

    if (aliased_devfn) {
        *aliased_devfn = devfn;
    }
}

AddressSpace *pci_device_iommu_address_space(PCIDevice *dev)
{
    PCIBus *bus;
    PCIBus *iommu_bus;
    int devfn;

    pci_device_get_iommu_bus_devfn(dev, &iommu_bus, &bus, &devfn);
    if (iommu_bus) {
        return iommu_bus->iommu_ops->get_address_space(bus,
                                 iommu_bus->iommu_opaque, devfn);
    }
    return &address_space_memory;
}

int pci_iommu_init_iotlb_notifier(PCIDevice *dev, IOMMUNotifier *n,
                                  IOMMUNotify fn, void *opaque)
{
    PCIBus *bus;
    PCIBus *iommu_bus;
    int devfn;

    pci_device_get_iommu_bus_devfn(dev, &bus, &iommu_bus, &devfn);
    if (iommu_bus && iommu_bus->iommu_ops->init_iotlb_notifier) {
        iommu_bus->iommu_ops->init_iotlb_notifier(bus, iommu_bus->iommu_opaque,
                                                  devfn, n, fn, opaque);
        return 0;
    }

    return -ENODEV;
}

bool pci_device_set_iommu_device(PCIDevice *dev, HostIOMMUDevice *hiod,
                                 Error **errp)
{
    PCIBus *iommu_bus, *aliased_bus;
    int aliased_devfn;

    /* set_iommu_device requires device's direct BDF instead of aliased BDF */
    pci_device_get_iommu_bus_devfn(dev, &iommu_bus,
                                   &aliased_bus, &aliased_devfn);
    if (iommu_bus && iommu_bus->iommu_ops->set_iommu_device) {
        hiod->aliased_bus = aliased_bus;
        hiod->aliased_devfn = aliased_devfn;
        return iommu_bus->iommu_ops->set_iommu_device(pci_get_bus(dev),
                                                      iommu_bus->iommu_opaque,
                                                      dev->devfn, hiod, errp);
    }
    return true;
}

void pci_device_unset_iommu_device(PCIDevice *dev)
{
    PCIBus *iommu_bus;

    pci_device_get_iommu_bus_devfn(dev, &iommu_bus, NULL, NULL);
    if (iommu_bus && iommu_bus->iommu_ops->unset_iommu_device) {
        return iommu_bus->iommu_ops->unset_iommu_device(pci_get_bus(dev),
                                                        iommu_bus->iommu_opaque,
                                                        dev->devfn);
    }
}

int pci_pri_request_page(PCIDevice *dev, uint32_t pasid, bool priv_req,
                         bool exec_req, hwaddr addr, bool lpig,
                         uint16_t prgi, bool is_read, bool is_write)
{
    PCIBus *bus;
    PCIBus *iommu_bus;
    int devfn;

    if (!dev->is_master ||
            ((pasid != PCI_NO_PASID) && !pcie_pasid_enabled(dev))) {
        return -EPERM;
    }

    if (!pcie_pri_enabled(dev)) {
        return -EPERM;
    }

    pci_device_get_iommu_bus_devfn(dev, &bus, &iommu_bus, &devfn);
    if (iommu_bus && iommu_bus->iommu_ops->pri_request_page) {
        return iommu_bus->iommu_ops->pri_request_page(bus,
                                                     iommu_bus->iommu_opaque,
                                                     devfn, pasid, priv_req,
                                                     exec_req, addr, lpig, prgi,
                                                     is_read, is_write);
    }

    return -ENODEV;
}

int pci_pri_register_notifier(PCIDevice *dev, uint32_t pasid,
                              IOMMUPRINotifier *notifier)
{
    PCIBus *bus;
    PCIBus *iommu_bus;
    int devfn;

    if (!dev->is_master ||
            ((pasid != PCI_NO_PASID) && !pcie_pasid_enabled(dev))) {
        return -EPERM;
    }

    pci_device_get_iommu_bus_devfn(dev, &bus, &iommu_bus, &devfn);
    if (iommu_bus && iommu_bus->iommu_ops->pri_register_notifier) {
        iommu_bus->iommu_ops->pri_register_notifier(bus,
                                                    iommu_bus->iommu_opaque,
                                                    devfn, pasid, notifier);
        return 0;
    }

    return -ENODEV;
}

void pci_pri_unregister_notifier(PCIDevice *dev, uint32_t pasid)
{
    PCIBus *bus;
    PCIBus *iommu_bus;
    int devfn;

    pci_device_get_iommu_bus_devfn(dev, &bus, &iommu_bus, &devfn);
    if (iommu_bus && iommu_bus->iommu_ops->pri_unregister_notifier) {
        iommu_bus->iommu_ops->pri_unregister_notifier(bus,
                                                      iommu_bus->iommu_opaque,
                                                      devfn, pasid);
    }
}

ssize_t pci_ats_request_translation(PCIDevice *dev, uint32_t pasid,
                                    bool priv_req, bool exec_req,
                                    hwaddr addr, size_t length,
                                    bool no_write, IOMMUTLBEntry *result,
                                    size_t result_length,
                                    uint32_t *err_count)
{
    PCIBus *bus;
    PCIBus *iommu_bus;
    int devfn;

    if (!dev->is_master ||
            ((pasid != PCI_NO_PASID) && !pcie_pasid_enabled(dev))) {
        return -EPERM;
    }

    if (result_length == 0) {
        return -ENOSPC;
    }

    if (!pcie_ats_enabled(dev)) {
        return -EPERM;
    }

    pci_device_get_iommu_bus_devfn(dev, &bus, &iommu_bus, &devfn);
    if (iommu_bus && iommu_bus->iommu_ops->ats_request_translation) {
        return iommu_bus->iommu_ops->ats_request_translation(bus,
                                                     iommu_bus->iommu_opaque,
                                                     devfn, pasid, priv_req,
                                                     exec_req, addr, length,
                                                     no_write, result,
                                                     result_length, err_count);
    }

    return -ENODEV;
}

int pci_iommu_register_iotlb_notifier(PCIDevice *dev, uint32_t pasid,
                                      IOMMUNotifier *n)
{
    PCIBus *bus;
    PCIBus *iommu_bus;
    int devfn;

    if ((pasid != PCI_NO_PASID) && !pcie_pasid_enabled(dev)) {
        return -EPERM;
    }

    pci_device_get_iommu_bus_devfn(dev, &bus, &iommu_bus, &devfn);
    if (iommu_bus && iommu_bus->iommu_ops->register_iotlb_notifier) {
        iommu_bus->iommu_ops->register_iotlb_notifier(bus,
                                           iommu_bus->iommu_opaque, devfn,
                                           pasid, n);
        return 0;
    }

    return -ENODEV;
}

int pci_iommu_unregister_iotlb_notifier(PCIDevice *dev, uint32_t pasid,
                                        IOMMUNotifier *n)
{
    PCIBus *bus;
    PCIBus *iommu_bus;
    int devfn;

    if ((pasid != PCI_NO_PASID) && !pcie_pasid_enabled(dev)) {
        return -EPERM;
    }

    pci_device_get_iommu_bus_devfn(dev, &bus, &iommu_bus, &devfn);
    if (iommu_bus && iommu_bus->iommu_ops->unregister_iotlb_notifier) {
        iommu_bus->iommu_ops->unregister_iotlb_notifier(bus,
                                                        iommu_bus->iommu_opaque,
                                                        devfn, pasid, n);
        return 0;
    }

    return -ENODEV;
}

int pci_iommu_get_iotlb_info(PCIDevice *dev, uint8_t *addr_width,
                             uint32_t *min_page_size)
{
    PCIBus *bus;
    PCIBus *iommu_bus;
    int devfn;

    pci_device_get_iommu_bus_devfn(dev, &bus, &iommu_bus, &devfn);
    if (iommu_bus && iommu_bus->iommu_ops->get_iotlb_info) {
        iommu_bus->iommu_ops->get_iotlb_info(iommu_bus->iommu_opaque,
                                             addr_width, min_page_size);
        return 0;
    }

    return -ENODEV;
}

void pci_setup_iommu(PCIBus *bus, const PCIIOMMUOps *ops, void *opaque)
{
    /*
     * If called, pci_setup_iommu() should provide a minimum set of
     * useful callbacks for the bus.
     */
    assert(ops);
    assert(ops->get_address_space);

    bus->iommu_ops = ops;
    bus->iommu_opaque = opaque;
}

static void pci_dev_get_w64(PCIBus *b, PCIDevice *dev, void *opaque)
{
    Range *range = opaque;
    uint16_t cmd = pci_get_word(dev->config + PCI_COMMAND);
    int i;

    if (!(cmd & PCI_COMMAND_MEMORY)) {
        return;
    }

    if (IS_PCI_BRIDGE(dev)) {
        pcibus_t base = pci_bridge_get_base(dev, PCI_BASE_ADDRESS_MEM_PREFETCH);
        pcibus_t limit = pci_bridge_get_limit(dev, PCI_BASE_ADDRESS_MEM_PREFETCH);

        base = MAX(base, 0x1ULL << 32);

        if (limit >= base) {
            Range pref_range;
            range_set_bounds(&pref_range, base, limit);
            range_extend(range, &pref_range);
        }
    }
    for (i = 0; i < PCI_NUM_REGIONS; ++i) {
        PCIIORegion *r = &dev->io_regions[i];
        pcibus_t lob, upb;
        Range region_range;

        if (!r->size ||
            (r->type & PCI_BASE_ADDRESS_SPACE_IO) ||
            !(r->type & PCI_BASE_ADDRESS_MEM_TYPE_64)) {
            continue;
        }

        lob = pci_bar_address(dev, i, r->type, r->size);
        upb = lob + r->size - 1;
        if (lob == PCI_BAR_UNMAPPED) {
            continue;
        }

        lob = MAX(lob, 0x1ULL << 32);

        if (upb >= lob) {
            range_set_bounds(&region_range, lob, upb);
            range_extend(range, &region_range);
        }
    }
}

void pci_bus_get_w64_range(PCIBus *bus, Range *range)
{
    range_make_empty(range);
    pci_for_each_device_under_bus(bus, pci_dev_get_w64, range);
}

static bool pcie_has_upstream_port(PCIDevice *dev)
{
    PCIDevice *parent_dev = pci_bridge_get_device(pci_get_bus(dev));

    /* Device associated with an upstream port.
     * As there are several types of these, it's easier to check the
     * parent device: upstream ports are always connected to
     * root or downstream ports.
     */
    return parent_dev &&
        pci_is_express(parent_dev) &&
        parent_dev->exp.exp_cap &&
        (pcie_cap_get_type(parent_dev) == PCI_EXP_TYPE_ROOT_PORT ||
         pcie_cap_get_type(parent_dev) == PCI_EXP_TYPE_DOWNSTREAM);
}

PCIDevice *pci_get_function_0(PCIDevice *pci_dev)
{
    PCIBus *bus = pci_get_bus(pci_dev);

    if(pcie_has_upstream_port(pci_dev)) {
        /* With an upstream PCIe port, we only support 1 device at slot 0 */
        return bus->devices[0];
    } else {
        /* Other bus types might support multiple devices at slots 0-31 */
        return bus->devices[PCI_DEVFN(PCI_SLOT(pci_dev->devfn), 0)];
    }
}

MSIMessage pci_get_msi_message(PCIDevice *dev, int vector)
{
    MSIMessage msg;
    if (msix_enabled(dev)) {
        msg = msix_get_message(dev, vector);
    } else if (msi_enabled(dev)) {
        msg = msi_get_message(dev, vector);
    } else {
        /* Should never happen */
        error_report("%s: unknown interrupt type", __func__);
        abort();
    }
    return msg;
}

/* 设置PCI设备的电源状态 */
void pci_set_power(PCIDevice *d, bool state)
{
    /*
     * 在设备上电/断电时不要改变VF的使能状态。
     *
     * 上电时，VF不能立即使能，必须等到客户机配置SR-IOV。
     * 断电时，对应的PF将被重置并禁用VF。
     */
    if (!pci_is_vf(d)) {
        /* 只对非虚拟功能设备设置使能状态 */
        pci_set_enabled(d, state);
    }
}

/* 设置PCI设备的使能状态 */
void pci_set_enabled(PCIDevice *d, bool state)
{
    /* 如果状态没有变化，直接返回 */
    if (d->enabled == state) {
        return;
    }

    /* 更新设备使能状态 */
    d->enabled = state;
    /* 更新内存映射 */
    pci_update_mappings(d);
    /* 根据命令寄存器和使能状态设置总线主控 */
    pci_set_master(d, (pci_get_word(d->config + PCI_COMMAND)
                      & PCI_COMMAND_MASTER) && d->enabled);
    /* 如果设备已实现，执行设备重置 */
    if (qdev_is_realized(&d->qdev)) {
        pci_device_reset(d);
    }
}

/* PCI设备类型信息结构 */
static const TypeInfo pci_device_type_info = {
    .name = TYPE_PCI_DEVICE,                    /* 类型名称 */
    .parent = TYPE_DEVICE,                      /* 父类型 */
    .instance_size = sizeof(PCIDevice),         /* 实例大小 */
    .abstract = true,                           /* 抽象类型 */
    .class_size = sizeof(PCIDeviceClass),       /* 类大小 */
    .class_init = pci_device_class_init,        /* 类初始化函数 */
    .class_base_init = pci_device_class_base_init, /* 类基础初始化函数 */
};

/* 注册PCI相关的所有类型 */
static void pci_register_types(void)
{
    /* 注册PCI总线类型 */
    type_register_static(&pci_bus_info);
    /* 注册PCIe总线类型 */
    type_register_static(&pcie_bus_info);
    /* 注册CXL总线类型 */
    type_register_static(&cxl_bus_info);
    /* 注册传统PCI接口类型 */
    type_register_static(&conventional_pci_interface_info);
    /* 注册CXL接口类型 */
    type_register_static(&cxl_interface_info);
    /* 注册PCIe接口类型 */
    type_register_static(&pcie_interface_info);
    /* 注册PCI设备类型 */
    type_register_static(&pci_device_type_info);
}

/* 类型初始化宏，在模块加载时自动调用 */
type_init(pci_register_types)
